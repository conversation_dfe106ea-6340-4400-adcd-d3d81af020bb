[2025-06-17 04:52:14] development.INFO: [AppController:banner] 1750135933728-req-01b70deb-1439-4208-88ef-51c808a56c94, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 04:53:22] development.INFO: [AppController:banner] 1750136002130-req-d06806ac-24d6-4e0b-a436-147aaca3ed37, banner called for {"user_id":"c1o2MDJqend3b0g4bzhtWWlpajV0ZWdQNGg2dkl0dVk0Unh4NXZFTFdMOD0="} 
[2025-06-17 04:53:35] development.INFO: [AppController:banner] 1750136015636-req-f23effc1-2a5c-4293-8573-b1a8b4a71807, banner called for {"user_id":"c1o2MDJqend3b0g4bzhtWWlpajV0ZWdQNGg2dkl0dVk0Unh4NXZFTFdMOD0="} 
[2025-06-17 04:53:45] development.INFO: [AppController:banner] 1750136025717-req-ae45a109-553f-4a6b-bfaf-c1522cd5c67c, banner called for {"user_id":"c1o2MDJqend3b0g4bzhtWWlpajV0ZWdQNGg2dkl0dVk0Unh4NXZFTFdMOD0="} 
[2025-06-17 04:54:08] development.INFO: [AppController:banner] 1750136048746-req-746730ec-5c02-4d78-bd71-7d298d86c385, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 05:44:43] development.INFO: [AppController:banner] 1750139081754-req-e185b612-5705-41e4-9d9b-ed196b5dcb14, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 05:49:29] development.INFO: [AppController:banner] 1750139369375-req-559054ea-d394-4b3a-8c0b-e21033dfa008, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:09:41] development.INFO: [AppController:banner] 1750144180339-req-ef348049-111f-41ba-80a3-d9ccf4b75eae, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:10:26] development.ERROR: No connection could be made because the target machine actively refused it {"exception":"[object] (RedisException(code: 0): No connection could be made because the target machine actively refused it at D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:175)
[stacktrace]
#0 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(175): Redis->connect('127.0.0.1', '6379', 0.0, NULL, 0, 0.0)
#1 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(88): Illuminate\\Redis\\Connectors\\PhpRedisConnector->establishConnection(Object(Redis), Array)
#2 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}(Object(Redis))
#3 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(79): tap(Object(Redis), Object(Closure))
#4 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(33): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient(Array)
#5 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#6 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(109): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect(Array, Array)
#7 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(90): Illuminate\\Redis\\RedisManager->resolve('default')
#8 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\RedisQueue.php(398): Illuminate\\Redis\\RedisManager->connection('default')
#9 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\RedisQueue.php(174): Illuminate\\Queue\\RedisQueue->getConnection()
#10 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\RedisQueue.php(159): Illuminate\\Queue\\RedisQueue->pushRaw('{\"uuid\":\"cd63df...', 'lists')
#11 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php(351): Illuminate\\Queue\\RedisQueue->Illuminate\\Queue\\{closure}('{\"uuid\":\"cd63df...', 'lists', NULL)
#12 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\RedisQueue.php(153): Illuminate\\Queue\\Queue->enqueueUsing(Object(App\\Jobs\\ProcessCampaignJob), '{\"uuid\":\"cd63df...', 'lists', NULL, Object(Closure))
#13 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(243): Illuminate\\Queue\\RedisQueue->push(Object(App\\Jobs\\ProcessCampaignJob), '', 'lists')
#14 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(227): Illuminate\\Bus\\Dispatcher->pushCommandToQueue(Object(Illuminate\\Queue\\RedisQueue), Object(App\\Jobs\\ProcessCampaignJob))
#15 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(76): Illuminate\\Bus\\Dispatcher->dispatchToQueue(Object(App\\Jobs\\ProcessCampaignJob))
#16 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\PendingDispatch.php(221): Illuminate\\Bus\\Dispatcher->dispatch(Object(App\\Jobs\\ProcessCampaignJob))
#17 D:\\xampp\\htdocs\\vira\\app\\Http\\Controllers\\AppController.php(715): Illuminate\\Foundation\\Bus\\PendingDispatch->__destruct()
#18 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AppController->storeLists(Object(Illuminate\\Http\\Request))
#19 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('storeLists', Array)
#20 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AppController), 'storeLists')
#21 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#22 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\xampp\\htdocs\\vira\\app\\Http\\Middleware\\SameOrigin.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\SameOrigin->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\xampp\\htdocs\\vira\\app\\Http\\Middleware\\ForceXmlHttpRequest.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ForceXmlHttpRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(160): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(126): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#32 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(88): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#33 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#34 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\xampp\\htdocs\\vira\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 D:\\xampp\\htdocs\\vira\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 D:\\xampp\\htdocs\\vira\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\xampp\\\\htdocs...')
#62 {main}
"} 
[2025-06-17 07:11:21] development.INFO: [AppController:banner] 1750144281925-req-5f74f656-0708-4620-8bb0-6c7a17fe63bb, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:12:14] development.INFO: [ProcessCampaignJob:handle] 1750144331670-campaign-345, processing: 345  
[2025-06-17 07:12:17] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 345  
[2025-06-17 07:12:17] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 345  
[2025-06-17 07:12:17] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 345  
[2025-06-17 07:12:17] development.INFO: [ProcessCampaignJob] Completed processing campaign: 345, processed: 3 contacts  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-345-contact-************, AccountId: 23  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Initialization completed in 160.02ms  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Validation completed in 35.43ms  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dock"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ZV8azITihrSuG5wc","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"4960773790354867","name":"mclaughlin-fisher-and-streich_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Template fetch and analysis completed in 646.5ms  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dock"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ZV8azITihrSuG5wc","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Dock"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ZV8azITihrSuG5wc","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:18] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Parameter preparation completed in 0.16ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - WhatsApp API call completed in 514.9ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:12:19] development.INFO: [dialog_updates] Redis length: 1 for campaign: 345  
[2025-06-17 07:12:19] development.INFO: [timeline_updates] Redis length: 1 for campaign: 345  
[2025-06-17 07:12:19] development.INFO: [campaign_updates] Redis length: 1 for campaign: 345  
[2025-06-17 07:12:19] development.INFO: [message_updates] Redis length: 1 for campaign: 345  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-345-contact-************ - Total execution time: 1532.4ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-345-contact-************ - Performance breakdown: Initialization: 160.02ms, Validation: 35.43ms, Template Fetch: 646.5ms, Parameter Preparation: 0.16ms, API Call: 514.9ms, Response Validation: 0.01ms, Total: 1532.4ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-345-contact-************ - Total execution time: 1532.97ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-345-contact-************, AccountId: 23  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Initialization completed in 2.59ms  
[2025-06-17 07:12:19] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Validation completed in 0.01ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Favian"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"jOY3yhslRXPpOUfb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"4960773790354867","name":"mclaughlin-fisher-and-streich_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Template fetch and analysis completed in 608.95ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Favian"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"jOY3yhslRXPpOUfb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Favian"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"jOY3yhslRXPpOUfb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Parameter preparation completed in 0.11ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - WhatsApp API call completed in 472.45ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:12:20] development.INFO: [dialog_updates] Redis length: 2 for campaign: 345  
[2025-06-17 07:12:20] development.INFO: [timeline_updates] Redis length: 2 for campaign: 345  
[2025-06-17 07:12:20] development.INFO: [campaign_updates] Redis length: 2 for campaign: 345  
[2025-06-17 07:12:20] development.INFO: [message_updates] Redis length: 2 for campaign: 345  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-345-contact-************ - Total execution time: 1105.79ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-345-contact-************ - Performance breakdown: Initialization: 2.59ms, Validation: 0.01ms, Template Fetch: 608.95ms, Parameter Preparation: 0.11ms, API Call: 472.45ms, Response Validation: 0.01ms, Total: 1105.79ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-345-contact-************ - Total execution time: 1106.27ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-345-contact-************, AccountId: 23  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Initialization completed in 2.6ms  
[2025-06-17 07:12:20] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Validation completed in 0.01ms  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Blaze"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"455S57XjrAhPPjLL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"4960773790354867","name":"mclaughlin-fisher-and-streich_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Template fetch and analysis completed in 499.5ms  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Blaze"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"455S57XjrAhPPjLL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Blaze"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"455S57XjrAhPPjLL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:12:21] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Parameter preparation completed in 0.22ms  
[2025-06-17 07:12:22] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - WhatsApp API call completed in 839.03ms  
[2025-06-17 07:12:22] development.INFO: [CampaignMessageJob:handle] *************-campaign-345-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:12:22] development.INFO: [dialog_updates] Redis length: 3 for campaign: 345  
[2025-06-17 07:12:22] development.INFO: [timeline_updates] Redis length: 3 for campaign: 345  
[2025-06-17 07:12:22] development.INFO: [campaign_updates] Redis length: 3 for campaign: 345  
[2025-06-17 07:12:22] development.INFO: [message_updates] Redis length: 3 for campaign: 345  
[2025-06-17 07:12:22] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-345-contact-************ - Total execution time: 1417.22ms  
[2025-06-17 07:12:22] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-345-contact-************ - Performance breakdown: Initialization: 2.6ms, Validation: 0.01ms, Template Fetch: 499.5ms, Parameter Preparation: 0.22ms, API Call: 839.03ms, Response Validation: 0.01ms, Total: 1417.22ms  
[2025-06-17 07:12:22] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-345-contact-************ - Total execution time: 1417.74ms  
[2025-06-17 07:13:03] development.INFO: [AppController:banner] 1750144383723-req-7315eb12-a73f-4206-955f-80d2362c6664, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Found 1 timeline keys: vira_database_timeline_updates:345  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_timeline_updates:345, Clean key: timeline_updates:345, Length: 3  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Dispatching TimelineBatchJob for campaign 345 with 3 items  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Found 1 campaign keys: vira_database_dialog_updates:345  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_dialog_updates:345, Clean key: dialog_updates:345, Length: 3  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Dispatching DailogBatchJob for campaign 345 with 3 items  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Found 1 campaign keys: vira_database_campaign_updates:345  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_campaign_updates:345, Clean key: campaign_updates:345, Length: 3  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Dispatching CampaignBatchJob for campaign 345 with 3 items  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Found 1 message keys: vira_database_message_updates:345  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Key: vira_database_message_updates:345, Clean key: message_updates:345, Length: 3  
[2025-06-17 07:13:24] development.INFO: [ProcessTimelineBatchesCommand] Dispatching MessageBatchJob for campaign 345 with 3 items  
[2025-06-17 07:13:25] development.INFO: [TimelineBatchJob:handle] 1750144404799-timeline-batch-345, processing batch for campaign: 345  
[2025-06-17 07:13:25] development.INFO: [TimelineBatchJob:handle] 1750144404799-timeline-batch-345, Retrieved {3} items from Redis key: timeline_updates:345  
[2025-06-17 07:13:26] development.INFO: [TimelineBatchJob:handle] 1750144404799-timeline-batch-345, Successfully processed batch of 3 items  
[2025-06-17 07:13:26] development.INFO: [TimelineBatchJob:handle] 1750144404799-timeline-batch-345, Completed in 1267.55 ms  
[2025-06-17 07:13:26] development.INFO: [DialogBatchJob:handle] 1750144404840-timeline-batch-345, processing batch for campaign: 345  
[2025-06-17 07:13:26] development.INFO: [DialogBatchJob:handle] 1750144404840-timeline-batch-345, Retrieved {3} items from Redis key: dialog_updates:345  
[2025-06-17 07:13:26] development.INFO: [DialogBatchJob] Inserted batch of 3 dialog records for campaign: 345  
[2025-06-17 07:13:26] development.INFO: [DialogBatchJob:handle] 1750144404840-timeline-batch-345, Completed in 55.71 ms  
[2025-06-17 07:13:26] development.INFO: [CampaignBatchJob:handle] 1750144404865-campaign-batch-345, processing batch for campaign: 345  
[2025-06-17 07:13:26] development.INFO: [CampaignBatchJob:handle] 1750144404865-campaign-batch-345, Retrieved {3} items from Redis key: campaign_updates:345  
[2025-06-17 07:13:26] development.INFO: [CampaignBatchJob:handle] 1750144404865-campaign-batch-345, Successfully processed batch of 3 campaign records  
[2025-06-17 07:13:26] development.INFO: [CampaignBatchJob:handle] 1750144404865-campaign-batch-345, Completed in 24.02 ms  
[2025-06-17 07:13:26] development.INFO: [MessageBatchJob:handle] 1750144404895-message-batch-345, processing batch for campaign: 345  
[2025-06-17 07:13:26] development.INFO: [MessageBatchJob:handle] 1750144404895-message-batch-345, Retrieved {3} items from Redis key: message_updates:345  
[2025-06-17 07:13:26] development.INFO: [MessageBatchJob:handle] 1750144404895-message-batch-345, Successfully processed batch of 3 message records  
[2025-06-17 07:13:26] development.INFO: [MessageBatchJob:handle] 1750144404895-message-batch-345, Completed in 183.86 ms  
[2025-06-17 07:13:48] development.INFO: [AppController:banner] 1750144428151-req-269d1f8b-2716-481d-a032-0851a475d84e, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:18:13] development.INFO: [AppController:banner] 1750144693081-req-0f9d7845-2f8f-4256-b58a-dae1d9bfbbe9, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
[2025-06-17 07:18:46] development.INFO: [ProcessCampaignJob:handle] 1750144725123-campaign-346, processing: 346  
[2025-06-17 07:18:47] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 346  
[2025-06-17 07:18:47] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 346  
[2025-06-17 07:18:47] development.INFO: [ProcessCampaignJob] Dispatched message for contact: ************, campaign: 346  
[2025-06-17 07:18:47] development.INFO: [ProcessCampaignJob] Completed processing campaign: 346, processed: 3 contacts  
[2025-06-17 07:18:47] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-346-contact-************, AccountId: 23  
[2025-06-17 07:18:47] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Initialization completed in 12.92ms  
[2025-06-17 07:18:47] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Validation completed in 0.01ms  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Luther"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"Qaa57Iv4uWbsaTtL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"5038866968650236","name":"bashirian-hahn_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Template fetch and analysis completed in 726.33ms  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Luther"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"Qaa57Iv4uWbsaTtL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] options{"phone":"918285652466","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Luther"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"Qaa57Iv4uWbsaTtL","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:48] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Parameter preparation completed in 0.24ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - WhatsApp API call completed in 707.75ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:18:49] development.INFO: [dialog_updates] Redis length: 1 for campaign: 346  
[2025-06-17 07:18:49] development.INFO: [timeline_updates] Redis length: 1 for campaign: 346  
[2025-06-17 07:18:49] development.INFO: [campaign_updates] Redis length: 1 for campaign: 346  
[2025-06-17 07:18:49] development.INFO: [message_updates] Redis length: 1 for campaign: 346  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-346-contact-************ - Total execution time: 1469.67ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-346-contact-************ - Performance breakdown: Initialization: 12.92ms, Validation: 0.01ms, Template Fetch: 726.33ms, Parameter Preparation: 0.24ms, API Call: 707.75ms, Response Validation: 0.01ms, Total: 1469.67ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-346-contact-************ - Total execution time: 1470.19ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-346-contact-************, AccountId: 23  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Initialization completed in 2.47ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Validation completed in 0.01ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Enoch"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"S4purINxPxsy1dvi","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"5038866968650236","name":"bashirian-hahn_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Template fetch and analysis completed in 698.41ms  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Enoch"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"S4purINxPxsy1dvi","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] options{"phone":"918400723243","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Enoch"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"S4purINxPxsy1dvi","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:49] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Parameter preparation completed in 0.32ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - WhatsApp API call completed in 799.21ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:18:50] development.INFO: [dialog_updates] Redis length: 2 for campaign: 346  
[2025-06-17 07:18:50] development.INFO: [timeline_updates] Redis length: 2 for campaign: 346  
[2025-06-17 07:18:50] development.INFO: [campaign_updates] Redis length: 2 for campaign: 346  
[2025-06-17 07:18:50] development.INFO: [message_updates] Redis length: 2 for campaign: 346  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-346-contact-************ - Total execution time: 1509.79ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-346-contact-************ - Performance breakdown: Initialization: 2.47ms, Validation: 0.01ms, Template Fetch: 698.41ms, Parameter Preparation: 0.32ms, API Call: 799.21ms, Response Validation: 0.01ms, Total: 1509.79ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-346-contact-************ - Total execution time: 1510.19ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] Running for *************-campaign-346-contact-************, AccountId: 23  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Initialization completed in 2.99ms  
[2025-06-17 07:18:50] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Validation completed in 0.01ms  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] Mock Template{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Elsie"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ay8IdJU8IEscXbdb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] Selected Template{"id":"5038866968650236","name":"bashirian-hahn_demo_0","mediaType":"NA","fields":[]}  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Template fetch and analysis completed in 904.6ms  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[],"templateData":[],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Elsie"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ay8IdJU8IEscXbdb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] options{"phone":"917702189036","params":[{"type":"text","text":"Default Name"}],"templateData":["Default Name"],"template":{"name":"mush_tem1","parameter_format":"POSITIONAL","components":[{"type":"HEADER","format":"TEXT","text":"Header Temp"},{"type":"BODY","text":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","example":{"body_text":[["Elsie"]]}},{"type":"FOOTER","text":"Foote Temp"}],"language":"en","status":"APPROVED","category":"MARKETING","id":"ay8IdJU8IEscXbdb","subType":null,"header":{"type":"HEADER","format":"TEXT","text":"Header Temp"},"params":1,"body":"Hi {{1}}
Welcome To Niswire Prod Test
Thanks","type":"text","mediaType":"text","buttons":null,"hasHeaderParam":false,"named":[]}}  
[2025-06-17 07:18:51] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Parameter preparation completed in 0.26ms  
[2025-06-17 07:18:52] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - WhatsApp API call completed in 824.38ms  
[2025-06-17 07:18:52] development.INFO: [CampaignMessageJob:handle] *************-campaign-346-contact-************ - Response validation completed in 0.01ms  
[2025-06-17 07:18:52] development.INFO: [dialog_updates] Redis length: 3 for campaign: 346  
[2025-06-17 07:18:52] development.INFO: [timeline_updates] Redis length: 3 for campaign: 346  
[2025-06-17 07:18:52] development.INFO: [campaign_updates] Redis length: 3 for campaign: 346  
[2025-06-17 07:18:52] development.INFO: [message_updates] Redis length: 3 for campaign: 346  
[2025-06-17 07:18:52] development.INFO: [CampaignMessageJob:handle] Success for *************-campaign-346-contact-************ - Total execution time: 1747.93ms  
[2025-06-17 07:18:52] development.INFO: [CampaignMessageJob:performance_summary] *************-campaign-346-contact-************ - Performance breakdown: Initialization: 2.99ms, Validation: 0.01ms, Template Fetch: 904.6ms, Parameter Preparation: 0.26ms, API Call: 824.38ms, Response Validation: 0.01ms, Total: 1747.93ms  
[2025-06-17 07:18:52] development.INFO: [CampaignMessageJob:handle] Completed for *************-campaign-346-contact-************ - Total execution time: 1748.29ms  
[2025-06-17 07:18:53] development.INFO: [AppController:banner] 1750144733193-req-4237beb8-dc71-4335-80e3-58687554dcb0, banner called for {"user_id":"WE95UEMvMXVCWU5NWjR0bnVsczRSMHBOMkZSOFFEVkVuRmtHdHBraXJNND0="} 
