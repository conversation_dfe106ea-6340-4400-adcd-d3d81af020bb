<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;

use Log;

class Func
{
    public static function findCountryCode($phone)
    {
        $countryCode = null;

        try {
            $phone = self::parsePhone($phone);
            $phone = str_replace('+', '', $phone);
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
            $mobile = $phoneUtil->parse('+' . $phone);
            if ($phoneUtil->isPossibleNumber($mobile)) {
                $countryCode = $mobile->getCountryCode();
            }

            return $countryCode;
        } catch (\Exception $e) {
            $countryCode = null;
        }

        return $countryCode;
    }

    public static function countryCodes()
    {
        $codes = [1 => 'US', 11 => 'AG', 12 => 'AI', 13 => 'AS', 14 => 'BB', 15 => 'BM', 16 => 'BS', 17 => 'CA', 18 => 'DM', 19 => 'DO', 110 => 'GD', 111 => 'GU', 112 => 'JM', 113 => 'KN', 114 => 'KY', 115 => 'LC', 116 => 'MP', 117 => 'MS', 118 => 'PR', 119 => 'SX', 120 => 'TC', 121 => 'TT', 122 => 'VC', 123 => 'VG', 124 => 'VI', 7 => 'RU', 71 => 'KZ', 20 => 'EG', 27 => 'ZA', 30 => 'GR', 31 => 'NL', 32 => 'BE', 33 => 'FR', 34 => 'ES', 36 => 'HU', 39 => 'IT', 391 => 'VA', 40 => 'RO', 41 => 'CH', 43 => 'AT', 44 => 'GB', 441 => 'GG', 442 => 'IM', 443 => 'JE', 45 => 'DK', 46 => 'SE', 47 => 'NO', 471 => 'SJ', 48 => 'PL', 49 => 'DE', 51 => 'PE', 52 => 'MX', 53 => 'CU', 54 => 'AR', 55 => 'BR', 56 => 'CL', 57 => 'CO', 58 => 'VE', 60 => 'MY', 61 => 'AU', 611 => 'CC', 612 => 'CX', 62 => 'ID', 63 => 'PH', 64 => 'NZ', 65 => 'SG', 66 => 'TH', 81 => 'JP', 82 => 'KR', 84 => 'VN', 86 => 'CN', 90 => 'TR', 91 => 'IN', 92 => 'PK', 93 => 'AF', 94 => 'LK', 95 => 'MM', 98 => 'IR', 211 => 'SS', 212 => 'MA', 2121 => 'EH', 213 => 'DZ', 216 => 'TN', 218 => 'LY', 220 => 'GM', 221 => 'SN', 222 => 'MR', 223 => 'ML', 224 => 'GN', 225 => 'CI', 226 => 'BF', 227 => 'NE', 228 => 'TG', 229 => 'BJ', 230 => 'MU', 231 => 'LR', 232 => 'SL', 233 => 'GH', 234 => 'NG', 235 => 'TD', 236 => 'CF', 237 => 'CM', 238 => 'CV', 239 => 'ST', 240 => 'GQ', 241 => 'GA', 242 => 'CG', 243 => 'CD', 244 => 'AO', 245 => 'GW', 246 => 'IO', 247 => 'AC', 248 => 'SC', 249 => 'SD', 250 => 'RW', 251 => 'ET', 252 => 'SO', 253 => 'DJ', 254 => 'KE', 255 => 'TZ', 256 => 'UG', 257 => 'BI', 258 => 'MZ', 260 => 'ZM', 261 => 'MG', 262 => 'RE', 2621 => 'YT', 263 => 'ZW', 264 => 'NA', 265 => 'MW', 266 => 'LS', 267 => 'BW', 268 => 'SZ', 269 => 'KM', 290 => 'SH', 2901 => 'TA', 291 => 'ER', 297 => 'AW', 298 => 'FO', 299 => 'GL', 350 => 'GI', 351 => 'PT', 352 => 'LU', 353 => 'IE', 354 => 'IS', 355 => 'AL', 356 => 'MT', 357 => 'CY', 358 => 'FI', 3581 => 'AX', 359 => 'BG', 370 => 'LT', 371 => 'LV', 372 => 'EE', 373 => 'MD', 374 => 'AM', 375 => 'BY', 376 => 'AD', 377 => 'MC', 378 => 'SM', 380 => 'UA', 381 => 'RS', 382 => 'ME', 383 => 'XK', 385 => 'HR', 386 => 'SI', 387 => 'BA', 389 => 'MK', 420 => 'CZ', 421 => 'SK', 423 => 'LI', 500 => 'FK', 501 => 'BZ', 502 => 'GT', 503 => 'SV', 504 => 'HN', 505 => 'NI', 506 => 'CR', 507 => 'PA', 508 => 'PM', 509 => 'HT', 590 => 'GP', 5901 => 'BL', 5902 => 'MF', 591 => 'BO', 592 => 'GY', 593 => 'EC', 594 => 'GF', 595 => 'PY', 596 => 'MQ', 597 => 'SR', 598 => 'UY', 599 => 'CW', 5991 => 'BQ', 670 => 'TL', 672 => 'NF', 673 => 'BN', 674 => 'NR', 675 => 'PG', 676 => 'TO', 677 => 'SB', 678 => 'VU', 679 => 'FJ', 680 => 'PW', 681 => 'WF', 682 => 'CK', 683 => 'NU', 685 => 'WS', 686 => 'KI', 687 => 'NC', 688 => 'TV', 689 => 'PF', 690 => 'TK', 691 => 'FM', 692 => 'MH', 800 => '001', 808 => '001', 850 => 'KP', 852 => 'HK', 853 => 'MO', 855 => 'KH', 856 => 'LA', 870 => '001', 878 => '001', 880 => 'BD', 881 => '001', 882 => '001', 883 => '001', 886 => 'TW', 888 => '001', 960 => 'MV', 961 => 'LB', 962 => 'JO', 963 => 'SY', 964 => 'IQ', 965 => 'KW', 966 => 'SA', 967 => 'YE', 968 => 'OM', 970 => 'PS', 971 => 'AE', 972 => 'IL', 973 => 'BH', 974 => 'QA', 975 => 'BT', 976 => 'MN', 977 => 'NP', 979 => '001', 992 => 'TJ', 993 => 'TM', 994 => 'AZ', 995 => 'GE', 996 => 'KG', 998 => 'UZ'];

        return $codes;
    }

    public static function parsePhone($phone)
    {
        $phone = ltrim($phone, '0');

        $parsePhone = preg_replace('/[^0-9]/', '', $phone);
        Str::startsWith($phone, '+') && $parsePhone = '+' . $parsePhone;

        return $parsePhone;
    }

    public static function makeChatId($phone)
    {
        $phone = self::parsePhone($phone);
        $phone = str_replace('+', '', $phone);

        return $phone;
    }

    public static function timestampToUtcMidnight($timestamp)
    {
        $date = new \DateTime("@$timestamp");
        $date->setTimezone(timezone_open('UTC'));
        $date->modify('midnight');

        return $date->getTimestamp() * 1000;
    }

    public static function countryByPhone($phone, $wantCode = false)
    {
        try {
            $phone = self::parsePhone($phone);
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
            $mobile = $phoneUtil->parse('+' . $phone);
            $phone = $phoneUtil->getRegionCodeForNumber($mobile) ?? '';
            $phone && $wantCode && $phone = $phoneUtil->getCountryCodeForRegion($phone);
        } catch (\Exception $e) {
            \Log::error("Invalid phone number provided Exception\n" . $e->getMessage());
            $phone = '';
        }

        return $phone;
    }

    public static function getNationalNumber($phone)
    {
        try {
            $phone = self::parsePhone($phone);
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
            $mobile = $phoneUtil->parse('+' . $phone);
            $regionCode = $phoneUtil->getRegionCodeForNumber($mobile) ?? '';
            $phone = $phoneUtil->parseAndKeepRawInput('+' . $phone, $regionCode);

            return $phone->getNationalNumber();
        } catch (\Exception $e) {
            \Log::error("Invalid phone number provided Exception\n" . $e->getMessage());
            $phone = '';
        }

        return $phone;
    }

    public static function applyCountryCode($phone, $countryCode)
    {
        $phone = self::parsePhone($phone);
        $countryCode = str_replace('+', '', $countryCode);

        $codes = self::countryCodes();
        $codeValue = $codes[$countryCode] ?? '';
        if (!$codeValue) {
            return $phone;
        }
        try {
            $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
            $isNumber = $phoneUtil->isPossibleNumber($phone, $codeValue);
            if (!$isNumber) {
                $codeValue = null;
                $phone = '+' . $phone;
            }
            $numberWithCountry = $phoneUtil->parse($phone, $codeValue);
            $phone = $phoneUtil->format($numberWithCountry, \libphonenumber\PhoneNumberFormat::E164);

            // \Log::info("[CRM] countryCode::$countryCode CodeValue::$codeValue IsNumber::$isNumber numberwithCountry::$numberWithCountry finalPhone::$phone");
            return $phone;
        } catch (\libphonenumber\NumberParseException $e) {
            \Log::warning("[CRM] Phone number parsing failed for phone: $phone, countryCode: $countryCode, error: " . $e->getMessage());
            return '';
        }
    }

    // builds message html body from saved message in table
    public static function messageToHtml($message)
    {
        $show = '';
        $message = (object) $message;
        switch ($message->file_type) {
            case 'image':
                $show = "<img src='$message->file_url' />";
                if ($message->body) {
                    $show .= "<br>$message->body";
                }

                break;
            case 'video':
                $show = "<video controls><source src='" . $message->file_url . "' type='video/mp4'></video>";
                if ($message->body) {
                    $show .= "<br>$message->body";
                }

                break;
            case 'ptt':
            case 'audio':
            case 'voice':
                $show = "<audio controls><source src='" . $message->file_url . "' type='audio/ogg'></audio>";
                if ($message->body) {
                    $show .= "<br>$message->body";
                }

                break;
            case 'document':
            case 'application':
            case 'file':
                $show = "<a href= '$message->file_url'>Document</a>";

                break;
            default:
                $show = $message->body;

                break;
        }

        return $show;
    }

    // makes hubspot timeline message from saved message
    public static function messageToTimeline($message)
    {
        $show = '';
        $file_type = $message->file_type ?? null;
        $file_type && $message->type = $file_type;
        switch ($message->type) {
            case 'image':
                if ($message->body) {
                    $show .= "$message->body \n\n";
                }
                $show .= "**Image:** [View image]($message->file_url)";

                break;

            case 'document':
            case 'application':
                $show = "[View Document]($message->file_url)";

                break;

            case 'audio':
            case 'voice':
                $show = "[View Audio]($message->file_url)";

                break;

            case 'video':
                $show = "[View Video]($message->file_url)";

                break;

            default:
                $show = $message->body;

                break;
        }

        return $show;
    }

    public static function checkInstaIdExists($portalId)
    {
        // Define the allowed portal IDs
        $allowedPortalIds = [7222284, 48611054, 44610097];

        // Check if the given portal ID is in the allowed list
        if (!in_array($portalId, $allowedPortalIds)) {
            return (object) [
                'status' => 'error',
            ];
        }

        // Return API response only if portal ID matches
        return Http::get(env('INSTA_URL') . "/auth/user?portalId=$portalId")->object();
    }

    public static function isMediaAllowed($mediaType, $mimeType, $fileSize)
    {
        $limits = [
            'audio' => [
                'audio/aac' => 16 * 1024 * 1024,
                'audio/amr' => 16 * 1024 * 1024,
                'audio/mpeg' => 16 * 1024 * 1024,
                'audio/mp4' => 16 * 1024 * 1024,
                'audio/ogg' => 16 * 1024 * 1024,
            ],
            'document' => [
                'text/plain' => 100 * 1024 * 1024,
                'application/vnd.ms-excel' => 100 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 100 * 1024 * 1024,
                'application/msword' => 100 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 100 * 1024 * 1024,
                'application/vnd.ms-powerpoint' => 100 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 100 * 1024 * 1024,
                'application/pdf' => 100 * 1024 * 1024,
            ],
            'image' => [
                'image/jpeg' => 5 * 1024 * 1024,
                'image/png' => 5 * 1024 * 1024,
                'image/webp' => 500 * 1024, // Using animated WebP limit as default
            ],
            'video' => [
                'video/3gpp' => 16 * 1024 * 1024,
                'video/mp4' => 16 * 1024 * 1024,
            ],
        ];

        return isset($limits[$mediaType][$mimeType]) && $fileSize <= $limits[$mediaType][$mimeType];
    }

    public static function getTimeZone($offset = 0)
    {
        try {
            $timezoneList = \DateTimeZone::listIdentifiers();

            $matchingTimezone = config('app.timezone');

            foreach ($timezoneList as $timezone) {
                $tz = new \DateTimeZone($timezone);
                $dt = new \DateTime('now', $tz);
                $timezoneOffset = $tz->getOffset($dt) / 60; // Convert seconds to minutes

                if ($timezoneOffset == -$offset) {
                    $matchingTimezone = $timezone;
                }
            }

            return $matchingTimezone;
        } catch (Exception $e) {
            return config('app.timezone');
        }
    }

    public static function pushToRedisAndDispatch(string $keyPrefix, string $campaignId, array $data, string $jobClass)
    {
        $redisKey = "{$keyPrefix}:{$campaignId}";
        $lockKey = "{$keyPrefix}:{$campaignId}:lock";

        Redis::rpush($redisKey, json_encode($data));
        Redis::expire($redisKey, 3600); // Set TTL to prevent memory bloat
        $length = Redis::llen($redisKey);
        Log::info("[{$keyPrefix}] Redis length: {$length} for campaign: {$campaignId}");

        if ($length >= 100) {
            $lock = Cache::lock($lockKey, 60);
            if ($lock->get()) {
                // Check length again after acquiring lock to prevent race conditions
                $currentLength = Redis::llen($redisKey);
                if ($currentLength >= 100) {
                    Log::info("[{$keyPrefix}] Lock acquired. Dispatching {$jobClass} for campaign: {$campaignId} with {$currentLength} items");
                    dispatch(new $jobClass($campaignId, $keyPrefix, $lockKey))->onQueue('lists');
                } else {
                    // Release lock if no longer needed
                    Cache::forget($lockKey);
                    Log::info("[{$keyPrefix}] Length dropped below 100 after lock acquisition. Releasing lock for campaign: {$campaignId}");
                }
            } else {
                Log::info("[{$keyPrefix}] Lock already held. Skipping dispatch for campaign: {$campaignId}");
            }
        }
    }

    /**
     * Process remaining items in Redis queue for completed campaigns
     */
    public static function processRemainingBatchItems(string $keyPrefix, string $campaignId, string $jobClass)
    {
        $redisKey = "{$keyPrefix}:{$campaignId}";
        $lockKey = "{$keyPrefix}:{$campaignId}:lock";

        $length = Redis::llen($redisKey);
        if ($length > 0) {
            $lock = Cache::lock($lockKey, 60);
            if ($lock->get()) {
                $currentLength = Redis::llen($redisKey);
                if ($currentLength > 0) {
                    Log::info("[{$keyPrefix}] Processing remaining {$currentLength} items for completed campaign: {$campaignId}");
                    dispatch(new $jobClass($campaignId, $keyPrefix, $lockKey))->onQueue('lists');
                } else {
                    Cache::forget($lockKey);
                }
            } else {
                Log::info("[{$keyPrefix}] Lock held, skipping remainder processing for campaign: {$campaignId}");
            }
        }
    }
}
