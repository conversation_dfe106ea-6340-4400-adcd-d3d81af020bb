<?php

namespace App\Jobs\Waba;

use Log;
use Exception;
use Throwable;
use App\Helpers\Func;
use App\Helpers\Waba;
use App\Models\Dialog;
use App\Models\Message;
use App\Hubspot\Hubspot;
use App\Models\Workflow;
use Illuminate\Support\Facades\Http;

class WhatsAppTextJob extends WhatsAppJob
{
    public function handle(): void
    {
        // Start overall timer
        $startOverall = microtime(true);
        Log::info("[WhatsAppTextJob:handle] running for $this->requestId");
        extract($this->input);

        try {
            // Start initialization timer
            $startInit = microtime(true);
            $accountId = $fields['account'];

            $phone = '';
            $options = [];
            $properties = $object['properties'] ?? [];
            $waba = new Waba($accountId, $this->requestId);
            $phone = $this->getPhoneFromProps($properties);
            $consentGiven = filter_var($properties['whatsapp_consent_given'] ?? false, FILTER_VALIDATE_BOOLEAN);
            if(config('services.whatsapp.use_mock_api')) {
            	$consentGiven = true;
            }
            $initDuration = round((microtime(true) - $startInit) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Initialization completed in {$initDuration}ms");

            // Start validation timer
            $startValidation = microtime(true);
            if (! $phone) {
                throw new Exception("contact doesn't have phone number", 1); // NOSONAR
            }

            if (! $consentGiven) {
                throw new Exception("contact doesn't have consent to send message", 1); // NOSONAR
            }

            // apply country code
            $portal = $waba->account;
            if (! $portal || $portal->paid < 1) {
                throw new Exception('Account is de-activated', 1); // NOSONAR
            }
            $validationDuration = round((microtime(true) - $startValidation) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Validation completed in {$validationDuration}ms");

            // Start phone formatting timer
            $startPhoneFormat = microtime(true);
            $portalSettings = $waba->getPortalSettings();
            if (substr($phone, 0, 1) != '+' && $portalSettings->country_code) {
                $countryCode = explode(':', $portalSettings->country_code)[0] ?? '';
                $countryCode && $phone = Func::applyCountryCode($phone, $countryCode);
            }
            $phone = $options['phone'] = Func::makeChatId($phone);
            $phoneFormatDuration = round((microtime(true) - $startPhoneFormat) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Phone formatting completed in {$phoneFormatDuration}ms");

            // Start message preparation timer
            $startMsgPrep = microtime(true);
            $messageBody = $fields['params'] ?? '';

            // Flag to track which path we're taking
            $isTextMessage = false;
            $isTemplateMessage = false;

            if (isset($fields['message_type']) && $fields['message_type'] == 'textmessage') {
                $isTextMessage = true;
                if (! $messageBody) {
                    throw new Exception('No text message value provided', 1); // NOSONAR
                }

                $options['message'] = $messageBody;
                $msgPrepDuration = round((microtime(true) - $startMsgPrep) * 1000, 2);
                Log::info("[WhatsAppTextJob:handle] $this->requestId - Text message preparation completed in {$msgPrepDuration}ms");
            } else {
                $isTemplateMessage = true;
                // Start template processing
                $startTemplateProc = microtime(true);
                $template = $waba->fetchTemplateById($fields['template']);
                if (! $template) {
                    throw new Exception('template not found', 1); // NOSONAR
                }
                $template = $this->buildTemplateParams($template);
                if (isset($fields['header_text_1'])) { // header param
                    $options['header_text_1'] = $fields['header_text_1'];
                }

                $options['params'] = [];
                $options['templateData'] = [];
                $options['template'] = $template;

                $paramcount = intval($fields['paramcount']);
                if ($paramcount && is_int($paramcount)) {
                    for ($i = 1; $i <= $paramcount; $i++) {
                        $indexKey = $paramcount;
                        $key = 'placeholder_'.$indexKey.'_'.$i;
                        $options['params'][] = ['type' => 'text', 'text' => $fields[$key]];
                        $options['templateData'][] = $fields[$key];
                    }
                }
                $templateProcDuration = round((microtime(true) - $startTemplateProc) * 1000, 2);
                Log::info("[WhatsAppTextJob:handle] $this->requestId - Template message preparation completed in {$templateProcDuration}ms");
            }

            // Start API call timer
            $startApiCall = microtime(true);
            if ($isTextMessage) {
                if (config('services.whatsapp.use_mock_api')) {
                    $randomPhone = mt_rand(1000000000, 9999999999); // Already 10 digits
                    $phone = $options['phone'] = Func::makeChatId('+91'.$randomPhone);
                    $sendText = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-text');
                    $res = json_decode($sendText->body());
                    $res->customData = $waba->makeCustomData($res);
                } else {
                    $res = $waba->sendText($options);
                }
                // $res = $waba->sendText($options);
            } else {
                if (config('services.whatsapp.use_mock_api')) {
                    $randomPhone = mt_rand(1000000000, 9999999999); // Already 10 digits
                    $phone = $options['phone'] = Func::makeChatId('+91'.$randomPhone);
                    $sendTemplate = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-template');
                    $res = json_decode($sendTemplate->body());
                    $res->customData = $waba->makeCustomData($res);

                } else {
                    $res = $waba->sendTemplate($options);
                }
                // $res = $waba->sendTemplate($options);
            }
            $apiCallDuration = round((microtime(true) - $startApiCall) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - WhatsApp API call completed in {$apiCallDuration}ms");

            // Start response validation timer
            $startRespValidation = microtime(true);
            if ($res === false || ! isset($res->customData['messageId'])) {
                throw new Exception('unable to send text message', 1); // NOSONAR
            }

            if ($isTemplateMessage) {
                $messageBody = $template->body;
                for ($i = 0; $i < count($options['templateData'] ?? []); $i++) {
                    $key = $i + 1;
                    $messageBody = str_replace('{{'.$key.'}}', $options['templateData'][$i], $messageBody);
                }
            }

            // change phone to correct waba id
            $phone = $this->getWabaId($res) ?: $phone;
            $respValidationDuration = round((microtime(true) - $startRespValidation) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Response validation completed in {$respValidationDuration}ms");

            // Start dialog save timer
            $startDialogSave = microtime(true);
            // save dialog
            $name = $this->getNameFromProps($properties);
            Dialog::updateOrCreate(['chatId' => $phone, 'account_id' => $accountId], [
                'object_id' => $object['objectId'],
                'phone' => $phone,
                'name' => $name,
                'time' => time(),
            ]);
            $dialogSaveDuration = round((microtime(true) - $startDialogSave) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Dialog save completed in {$dialogSaveDuration}ms");

            // Start message save timer
            $startMsgSave = microtime(true);
            // save message
            $type = $fields['file_type'] ?? 'text';
            $message = [
                'account_id' => $accountId,
                'chatId' => $phone,
                'id' => $res->customData['messageId'],
                'type' => $type,
                'body' => $messageBody,
                'from' => $res->customData['from'],
                'to' => $phone,
                'fromMe' => 1,
                'status' => 'sent',
                'time' => time(),
            ];
            isset($fields['file_type']) && $message['file_type'] = $fields['file_type'];
            isset($fields['file_url']) && $message['file_url'] = $fields['file_url'];
            $savedMessage = Message::create($message);
            $msgSaveDuration = round((microtime(true) - $startMsgSave) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Message save completed in {$msgSaveDuration}ms");

            // Start Hubspot timeline update timer
            $startHubspotUpdate = microtime(true);
            // update hubspot timeline
            $hsApp = new Hubspot($portal->portal_id, $this->requestId);
            $hsApp->timeline()->update([
                'id' => $res->customData['messageId'],
                'objectId' => $object['objectId'],
                'data' => [
                    'status' => 'sent',
                    'phone' => $portal->waba_phone,
                    'message' => Func::messageToTimeline($savedMessage),
                ],
            ], true);
            $hubspotUpdateDuration = round((microtime(true) - $startHubspotUpdate) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Hubspot timeline update completed in {$hubspotUpdateDuration}ms");

            // Start workflow creation timer
            $startWorkflowCreate = microtime(true);
            $workflowParams = [
                'name' => $name,
                'phone' => $phone,
                'from' => $message['from'],
                'accountId' => $accountId,
                'messageId' => $message['id'],
                'object_id' => $object['objectId'],
                'template_id' => $template->id ?? null,
                'template_name' => $template->name ?? null,
                'messageType' => $message['type'],
                'provider_request' => $waba->request,
                'provider_response' => $waba->response,
            ];
            $workflowData = $this->prepareWorkflowData($workflowParams);

            // Create workflow record
            Workflow::create($workflowData);
            $workflowCreateDuration = round((microtime(true) - $startWorkflowCreate) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] $this->requestId - Workflow creation completed in {$workflowCreateDuration}ms");

            $overallDuration = round((microtime(true) - $startOverall) * 1000, 2);
            Log::info("[WhatsAppTextJob:handle] Success for $this->requestId - Total execution time: {$overallDuration}ms");

            // Summary log of all timings
            Log::info("[WhatsAppTextJob:performance_summary] $this->requestId - Performance breakdown: " .
                "Initialization: {$initDuration}ms, " .
                "Validation: {$validationDuration}ms, " .
                "Phone Formatting: {$phoneFormatDuration}ms, " .
                ($isTextMessage ? "Text Msg Prep: {$msgPrepDuration}ms, " : "Template Proc: {$templateProcDuration}ms, ") .
                "API Call: {$apiCallDuration}ms, " .
                "Response Validation: {$respValidationDuration}ms, " .
                "Dialog Save: {$dialogSaveDuration}ms, " .
                "Message Save: {$msgSaveDuration}ms, " .
                "Hubspot Update: {$hubspotUpdateDuration}ms, " .
                "Workflow Creation: {$workflowCreateDuration}ms, " .
                "Total: {$overallDuration}ms"
            );
        } catch (Exception $e) {
            $trace = $e->getTraceAsString();
            $overallDuration = round((microtime(true) - $startOverall) * 1000, 2);
            Log::error("[WhatsAppTextJob:handle] $this->requestId, Exception after {$overallDuration}ms: ".$e->getMessage().', trace: '.$trace);
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[WhatsAppTextJob:failed] $this->requestId, Exception: ".$e->getMessage());
    }
}
