{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.2", "hubspot/api-client": "^12.0", "laravel/framework": "^12.0", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/slack-notification-channel": "^3.5", "laravel/tinker": "^2.8", "netflie/whatsapp-cloud-api": "dev-main", "opcodesio/log-viewer": "^3.15", "pusher/pusher-php-server": "^7.2", "ramsey/uuid": "^4.7", "stripe/stripe-php": "^10.19"}, "require-dev": {"fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.59", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": [{"type": "vcs", "url": "https://github.com/pravnyadv/whatsapp-cloud-api"}], "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}