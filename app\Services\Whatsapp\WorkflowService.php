<?php

namespace App\Services\Whatsapp;

use Log;
use App\Helpers\Waba;
use Illuminate\Support\Facades\Http;

class WorkflowService extends Waba
{
    public function __construct($accountId = null, $requestId = null)
    {
        parent::__construct($accountId, $requestId);
    }

    public function sendMediaTemplate($options)
    {
        $data = [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $options['phone'],
            'type' => 'template',
            'template' => [
                'name' => $options['template']->name,
                'language' => [
                    'code' => $options['template']->language,
                ],
            ],
        ];

        $header = [];
        $components = [];
        $template = $options['template'];
        $mediaType = $options['media_type'] ?? null;
        $buttonType = $options['button_type'] ?? null;

        if (isset($template->header)) {
            $headerType = strtolower($template->header->format);
            if ($headerType == 'text' && isset($options['header_text_1'])) {
                $components[] = [
                    'type' => 'header',
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => $options['header_text_1'],
                        ],
                    ],
                ];
            } elseif ($headerType == 'location') {
                $location = [];
                $location['latitude'] = $options[$headerType.'_latitude'] ?? '';
                $location['longitude'] = $options[$headerType.'_longitude'] ?? '';
                $location['name'] = $options[$headerType.'_location_name'] ?? '';
                $location['address'] = $options[$headerType.'_location_address'] ?? '';
                $header['location'] = $location;
            } else {
                $link = $options[$headerType.'_url'] ?? null;
                if ($link) {
                    $headerTypeData = ['link' => $link];
                    if ($headerType == 'document') {
                        $headerTypeData['filename'] = basename($headerTypeData['link']);
                    }
                    $header[$headerType] = $headerTypeData;
                }
            }
            $header && $components[] = ['type' => 'header', 'parameters' => [
                array_merge(['type' => $headerType], $header),
            ]];
        }

        // handle params
        $params = $options['params'] ?? [];
        if ($params) {
            $params = [
                'type' => 'body',
                'parameters' => $options['params'],
            ];
            $components[] = $params;
        }

        // handle buttons
        $buttonFlowId = null;
        $hasCopyButton = false;
        $dynamicButtonCount = 0;
        $hasDynamicButton = false;
        $buttons = $template->buttons ?? [];
        $buttonComponents = [];

        foreach ($buttons as $index => $buttonData) {
            $button = [];
            if ($buttonData->type == 'COPY_CODE') {
                $hasCopyButton = true;
                $key = 'button_copy';
                if ($buttonType != $key) {
                    $key = $buttonType.'_copy';
                }

                $button = [
                    'type' => 'button',
                    'index' => $index,
                    'sub_type' => 'copy_code',
                    'parameters' => [
                        [
                            'type' => 'coupon_code',
                            'coupon_code' => $options[$key] ?? '',
                        ],
                    ],
                ];
            }

            if ($buttonData->type == 'FLOW') {
                $button = [
                    'type' => 'button',
                    'index' => $index,
                    'sub_type' => 'flow',
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => $buttonData->text,
                        ],
                    ],
                ];
                $buttonFlowId = $buttonData->flow_id ?? null;
            }

            if ($buttonData->type == 'URL') {
                $paramCount = preg_match_all('/\{\{\d+\}\}/', $buttonData->url);
                if (! $paramCount) {
                    continue;
                }
                $dynamicButtonCount++;
                $hasDynamicButton = true;

                $button = [
                    'type' => 'button',
                    'index' => $index,
                    'sub_type' => 'url',
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => $options[$buttonType.'_button_'.$dynamicButtonCount] ?? '',
                        ],
                    ],
                ];
            }
            $button && ($components[] = $button);
        }

        $data['template']['components'] = $components;
        $this->request = json_encode($data);

        Log::info("[SendService:sendTemplate] $this->requestId, request payload: ".$this->request);

        $response = Http::withToken(env('WABA_TOKEN'))->post($this->messageUrl, $data)->object();
        $this->response = json_encode($response);
        Log::info("[SendService:sendTemplate] $this->requestId, response payload: ".$this->response);

        if (! isset($response->messages)) {
            return false;
        }

        $response->customData = $this->makeCustomData($response);
        $buttonFlowId && $response->customData['flowId'] = $buttonFlowId;

        return $response;
    }
}
