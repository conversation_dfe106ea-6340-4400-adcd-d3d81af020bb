<?php

namespace App\Console\Commands;

use Log;
use App\Helpers\Func;
use App\Jobs\TimelineBatchJob;
use App\Jobs\CampaignBatchJob;
use App\Jobs\MessageBatchJob;
use App\Jobs\DialogBatchJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class ProcessRemainingBatchesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batches:process-remaining';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process remaining items in batch queues for completed campaigns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing remaining batch items...');
        
        $this->processRemainingTimelines();
        $this->processRemainingCampaigns();
        $this->processRemainingMessages();
        $this->processRemainingDialogs();
        
        $this->info('Completed processing remaining batch items.');
    }

    private function processRemainingTimelines()
    {
        $keys = Redis::keys('*timeline_updates:*');
        
        foreach ($keys as $key) {
            // Extract campaign ID from key
            $campaignId = preg_replace('/.*timeline_updates:(\d+)/', '$1', $key);
            
            $length = Redis::llen($key);
            
            if ($length > 0) {
                $this->info("Found {$length} pending timeline updates for campaign {$campaignId}");
                Log::info("[ProcessRemainingBatchesCommand] Processing remaining timeline items for campaign {$campaignId} with {$length} items");
                Func::processRemainingBatchItems('timeline_updates', $campaignId, TimelineBatchJob::class);
            }
        }
    }

    private function processRemainingCampaigns()
    {
        $keys = Redis::keys('*campaign_updates:*');
        
        foreach ($keys as $key) {
            // Extract campaign ID from key
            $campaignId = preg_replace('/.*campaign_updates:(\d+)/', '$1', $key);
            
            $length = Redis::llen($key);
            
            if ($length > 0) {
                $this->info("Found {$length} pending campaign updates for campaign {$campaignId}");
                Log::info("[ProcessRemainingBatchesCommand] Processing remaining campaign items for campaign {$campaignId} with {$length} items");
                Func::processRemainingBatchItems('campaign_updates', $campaignId, CampaignBatchJob::class);
            }
        }
    }

    private function processRemainingMessages()
    {
        $keys = Redis::keys('*message_updates:*');
        
        foreach ($keys as $key) {
            // Extract campaign ID from key
            $campaignId = preg_replace('/.*message_updates:(\d+)/', '$1', $key);
            
            $length = Redis::llen($key);
            
            if ($length > 0) {
                $this->info("Found {$length} pending message updates for campaign {$campaignId}");
                Log::info("[ProcessRemainingBatchesCommand] Processing remaining message items for campaign {$campaignId} with {$length} items");
                Func::processRemainingBatchItems('message_updates', $campaignId, MessageBatchJob::class);
            }
        }
    }

    private function processRemainingDialogs()
    {
        $keys = Redis::keys('*dialog_updates:*');
        
        foreach ($keys as $key) {
            // Extract campaign ID from key
            $campaignId = preg_replace('/.*dialog_updates:(\d+)/', '$1', $key);
            
            $length = Redis::llen($key);
            
            if ($length > 0) {
                $this->info("Found {$length} pending dialog updates for campaign {$campaignId}");
                Log::info("[ProcessRemainingBatchesCommand] Processing remaining dialog items for campaign {$campaignId} with {$length} items");
                Func::processRemainingBatchItems('dialog_updates', $campaignId, DialogBatchJob::class);
            }
        }
    }
}
