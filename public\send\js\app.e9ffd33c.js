(function(){"use strict";var e={1076:function(e,t,s){var o=s(5471),a=function(){var e=this,t=e._self._c;return t("div",[!e.userData||e.loggedOut?t("app-logout"):t("app-layout")],1)},i=[],n=(s(4603),s(7566),s(8721),s(5353)),r=function(){var e=this;e._self._c;return e._m(0)},l=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"logout-view"},[t("h1",[e._v("You are not logged in.")])])}],c={name:"Logout"},d=c,u=s(1656),m=(0,u.A)(d,r,l,!1,null,"55bdeeee",null),g=m.exports,p=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"app-layout",class:{mod:e.modify}},[t("div",{staticClass:"app-layout_main",class:{inactive:!e.active}},[t("router-view")],1)])])},h=[],f={name:"Layout",components:{},computed:{active(){return this.$store.state.menuBarShow},modify(){const e=this.$route.name;return"Activities"===e}}},v=f,y=(0,u.A)(v,p,h,!1,null,null,null),A=y.exports,b=JSON.parse('{"admin":1,"channel":"wa_web","csrf":"AAccUfslE0n03WmoM0ceyoxYwYLGf0e1zBxlpgWj9Tyn1CZy1aybVRoqEn2Ylgt1tF70KYMdLrkDIMXsGDcIGt9Oo3qf6qaK-Q","domain":"niswey.com","email":"<EMAIL>","fcm_token":"e58bTbFvnZofQ5EHPeY6Ap:APA91bE-3c3QProZXR9_DCvr1sLWcwIG7xHRxgznVnvEdz5XYOu7Wmns7Kh_fQjI9QCqVy4HnmUUC_-V8IZvek2Kz8gDDrNunEXbs8EnX8t1YBPZ6DISFw0A_DGzxCKFC8vMBq8qQdRd","instance_id":1,"instances":[{"id":1,"phone":"************","status":1,"u_id":561,"users":null,"version":1}],"subscription":{"instanceId":1,"sub_id":"sss","portal_id":7222284},"level":2,"message":"User has instance_id","name":"Mayank","origin":"https://app.hubspot.com","owner":1,"portal_id":"7222284","portal_name":"Niswey","status":"ok","uid":561,"user_id":"QW1mWGZ0NUxZUjJmNjdzSUZXL1h2RXVJbkJ3SytkaTdmSURrTjl4Wm15QT0="}'),_={name:"App",components:{AppLayout:A,AppLogout:g},data(){return{testing:!1,interval:null}},computed:{...(0,n.aH)(["userData","loggedOut"])},watch:{loggedOut(){this.loggedOut&&clearInterval(this.interval)}},methods:{...(0,n.PY)(["setUserData","setErrorApp","setErrorMsgApp","setBanner","setConflict","setBannerContent"]),initApp(){let e={};var t=window.location.href,s=new URL(t);e.user_id=s.searchParams.get("user_id"),e.portal_id=s.searchParams.get("portal_id"),e.accountUser=s.searchParams.get("accountUser"),e.accountPhone=s.searchParams.get("accountPhone"),this.testing?this.setUserData(b):this.setUserData(e)},updateOnlineStatus(e){const{type:t}=e;"online"===t?window.location.reload():(this.setErrorApp(!0),this.setErrorMsgApp("You are offline!"))}},mounted(){this.initApp()}},w=_,C=(0,u.A)(w,a,i,!1,null,null,null),k=C.exports,M=s(173),I=function(){var e=this,t=e._self._c;return t("div",{staticClass:"activities-main-div"},[e.dataLoaded?e.noNeedToPermission||e.isAdmin||e.isPermission?t("chat-window",{attrs:{"user-data":e.userData,"current-user-id":e.currentUserId,rooms:e.rooms,engagement:e.engagement,participants:e.participants,loadingTab:e.loadingTab,showAddToHubspot:e.showAddToHubspot,showParticipants:e.showParticipants,setContactObjects:e.setContactObjects,"rooms-loaded":e.roomsLoaded,messages:e.messages,"messages-loaded":e.messagesLoaded,"selected-room":e.selectedRoom,requests:e.requests,request:e.request,labels:e.labels,"show-labels":e.showLabels,"room-labels":e.roomLabels,"assigning-label":e.assigningLabel,templates:e.templates,"error-message":e.errorMessage,"success-message":e.successMessage,"save-key":e.saveKey,"show-error-modal":e.showErrorModal,"show-create-modal":e.showCreateModal,"show-success-modal":e.showSuccessModal,"toggle-error-modal":e.toggleErrorModal,"toggle-success-modal":e.toggleSuccessModal,"loading-rooms":e.loadingRooms,"unread-counts":e.unreadCounts,messageInTransit:e.messageInTransit,"sidebar-visible":e.menuBarShow,isMsgFetched:e.isMsgFetched},on:{"room-action-handler":e.roomActionHandler,"send-message":e.sendMessage,"fetch-messages":e.fetchMessages,"open-file":e.openFile,"toggle-labels-modal":e.toggleLabelsModal,"close-sidebar":e.closeSideBar,"redirect-to-hubspot":e.redirectToHubspot,"add-template-msg":e.addTemplateMsg}}):t("Info",{attrs:{infoMsg:"You don't have chat access, Please ask admin for access."}}):t("spinner")],1)},x=[],S=(s(4114),function(){var e=this,t=e._self._c;return t("div",{staticClass:"mw-100"},[t("div",{staticClass:"vac-card-window",style:[e.cssVars]},[t("div",{staticClass:"vac-chat-container",class:{"profile-visible":e.showProfile}},[t("room",{attrs:{"current-user-id":e.currentUserId,rooms:e.rooms,"room-id":e.room.roomId||"","load-first-room":e.loadFirstRoom,messages:e.messages,"room-message":e.roomMessage,"messages-loaded":e.messagesLoaded,"menu-actions":e.menuActions,"message-actions":e.messageActions,"show-send-icon":e.showSendIcon,"show-files":e.showFiles,"show-audio":e.showAudio,"audio-bit-rate":e.audioBitRate,"audio-sample-rate":e.audioSampleRate,"show-emojis":e.showEmojis,"show-reaction-emojis":e.showReactionEmojis,"show-new-messages-divider":e.showNewMessagesDivider,"show-footer":e.showFooter,"text-messages":e.t,"single-room":e.singleRoom,"show-rooms-list":e.showRoomsList,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"is-mobile":!1,"loading-rooms":e.loadingRooms,"textarea-action-enabled":e.textareaActionEnabled,"accepted-files":e.acceptedFiles,"templates-text":e.templatesText,templates:e.templates,isMsgFetched:e.isMsgFetched,"unread-counts":e.unreadCounts,messageInTransit:e.messageInTransit},on:{"toggle-error-modal":e.toggleErrorModal,"fetch-messages":e.fetchMessages,"send-message":e.sendMessage,"edit-message":e.editMessage,"delete-message":e.deleteMessage,"open-file":e.openFile,"send-message-reaction":e.sendMessageReaction,"typing-message":e.typingMessage,"textarea-action-handler":e.textareaActionHandler,"carousel-handler":e.carouselHandler,"toggle-menu-bar":function(t){return e.$emit("toggle-menu-bar")},"add-template-msg":e.forwardTemplateMsg,"redirect-to-hubspot":function(t){return e.$emit("redirect-to-hubspot",e.room)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)]),t("app-carousel",{attrs:{show:e.showCarousel,close:e.closeCarousel,images:e.carouselData}}),t("error-modal",{attrs:{show:e.showErrorModal,toggle:e.toggleErrorModal,"error-message":this.errorMessage}}),t("success-modal",{attrs:{show:e.showSuccessModal,toggle:e.toggleSuccessModal,"success-message":e.successMessage}}),t("image-viewer",{attrs:{show:e.previewImage,msg:e.previewMessage,close:e.closeImageViewer}})],1)}),T=[],F=function(){var e=this,t=e._self._c;return e.show?t("div",{staticClass:"app-carousel",attrs:{id:"carousel"}},[t("div",{staticClass:"app-carousel-topbar"},[t("div",{staticClass:"image-details"},[t("span",{staticClass:"image-name"},[e._v(e._s(e.senderName))]),t("span",{staticClass:"image-time"},[e._v(e._s(e.imageTime))])]),t("div",{staticClass:"d-flex align-items-center"},[t("div",{on:{click:function(t){return e.$emit("open-forward-modal",{_id:e.images[e.activeImage].msg_id})}}},[t("svg",{staticClass:"forward-icon",attrs:{viewBox:"0 0 24 24",width:"24",height:"24"}},[t("path",{attrs:{fill:"currentColor",d:"M14.278 4.813c0-.723.873-1.085 1.383-.574l6.045 6.051a.81.81 0 0 1 0 1.146l-6.045 6.051a.81.81 0 0 1-1.383-.574v-2.732c-5.096 0-8.829 1.455-11.604 4.611-.246.279-.702.042-.602-.316 1.43-5.173 4.925-10.004 12.206-11.045V4.813z"}})])]),t("div",{on:{click:e.openFile}},[t("svg-icon",{attrs:{name:"document"}})],1),t("button",{staticClass:"app-carousel-topbar_close",on:{click:e.close}},[e._v("×")])])]),t("div",{staticClass:"app-carousel-main"},[t("div",{staticClass:"content"},[t("div",{staticClass:"carousel-img"},[t("img",{attrs:{src:e.currentImage,alt:"imageName"}})])])]),t("div",{staticClass:"app-carousel-thumbnails"},e._l(e.images,(function(s,o){return t("div",{key:s.id,class:["thumbnail-image",e.activeImage==o?"active":""],on:{click:function(t){return e.activateImage(o)}}},[t("img",{attrs:{src:s.thumb}})])})),0),t("div",{staticClass:"app-carousel-actions"},[t("span",{staticClass:"prev",on:{click:e.prevImage}},[t("svg",{attrs:{viewBox:"0 0 30 30",width:"30",height:"30"}},[t("path",{attrs:{fill:"currentColor",d:"M19.214 21.212L12.865 15l6.35-6.35-1.933-1.932L9 15l8.282 8.282 1.932-2.07z"}})])]),t("span",{staticClass:"next",on:{click:e.nextImage}},[t("svg",{attrs:{viewBox:"0 0 30 30",width:"30",height:"30"}},[t("path",{attrs:{fill:"currentColor",d:"M11 21.212L17.35 15 11 8.65l1.932-1.932L21.215 15l-8.282 8.282L11 21.212z"}})])])])]):e._e()},R=[],E=function(){var e=this,t=e._self._c;return t("svg",{attrs:{title:e.reason,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",version:"1.1",width:"24",height:"24",viewBox:`0 0 ${e.size} ${e.size}`}},[t("path",{attrs:{id:e.svgId,d:e.svgItem[e.name].path}}),e.svgItem[e.name].path2?t("path",{attrs:{id:e.svgId,d:e.svgItem[e.name].path2}}):e._e()])},L=[],B={name:"SvgIcon",props:{name:{type:String,default:null},param:{type:String,default:null},reason:{type:String,default:null}},data(){return{svgItem:{search:{path:"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"},add:{path:"M17,13H13V17H11V13H7V11H11V7H13V11H17M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"},toggle:{path:"M5,13L9,17L7.6,18.42L1.18,12L7.6,5.58L9,7L5,11H21V13H5M21,6V8H11V6H21M21,16V18H11V16H21Z"},menu:{path:"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"},close:{path:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"},cancel:{path:"M443.6,387.1L312.4,255.4l131.5-130c5.4-5.4,5.4-14.2,0-19.6l-37.4-37.6c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4  L256,197.8L124.9,68.3c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4L68,105.9c-5.4,5.4-5.4,14.2,0,19.6l131.5,130L68.4,387.1  c-2.6,2.6-4.1,6.1-4.1,9.8c0,3.7,1.4,7.2,4.1,9.8l37.4,37.6c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1L256,313.1l130.7,131.1  c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1l37.4-37.6c2.6-2.6,4.1-6.1,4.1-9.8C447.7,393.2,446.2,389.7,443.6,387.1z"},file:{path:"M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z"},paperclip:{path:"M1.816 15.556v.002c0 1.502.584 2.912 1.646 3.972s2.472 1.647 3.974 1.647a5.58 5.58 0 0 0 3.972-1.645l9.547-9.548c.769-.768 1.147-1.767 1.058-2.817-.079-.968-.548-1.927-1.319-2.698-1.594-1.592-4.068-1.711-5.517-.262l-7.916 7.915c-.881.881-.792 2.25.214 3.261.959.958 2.423 1.053 3.263.215l5.511-5.512c.28-.28.267-.722.053-.936l-.244-.244c-.191-.191-.567-.349-.957.04l-5.506 5.506c-.18.18-.635.127-.976-.214-.098-.097-.576-.613-.213-.973l7.915-7.917c.818-.817 2.267-.699 3.23.262.5.501.802 1.1.849 1.685.051.573-.156 1.111-.589 1.543l-9.547 9.549a3.97 3.97 0 0 1-2.829 1.171 3.975 3.975 0 0 1-2.83-1.173 3.973 3.973 0 0 1-1.172-2.828c0-1.071.415-2.076 1.172-2.83l7.209-7.211c.157-.157.264-.579.028-.814L11.5 4.36a.572.572 0 0 0-.834.018l-7.205 7.207a5.577 5.577 0 0 0-1.645 3.971z"},"close-outline":{path:"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"},send:{path:"M2,21L23,12L2,3V10L17,12L2,14V21Z"},emoji:{path:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"},document:{path:"M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"},pencil:{path:"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"},checkmark:{path:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"},"double-checkmark":{path:"M18 7l-1.41-1.41-12.59 11.41 2 2L18 7zm4.24-1.41L11.66 16.17 6 11l-1.41 1.41L11.66 19l12-12-1.42-1.41zM.41 13.41 6 19l1.41-1.41L1.83 12 .41 13.41z"},wait:{path:"M 5.832031 1.042969 C 3.1875 1.042969 1.042969 3.1875 1.042969 5.832031 L 1.042969 14.167969 C 1.042969 16.8125 3.1875 18.957031 5.832031 18.957031 L 14.167969 18.957031 C 16.8125 18.957031 18.957031 16.8125 18.957031 14.167969 L 18.957031 5.832031 C 18.957031 3.1875 16.8125 1.042969 14.167969 1.042969 Z M 5.832031 1.042969 M 10 3.542969 C 10.34375 3.542969 10.625 3.820312 10.625 4.167969 L 10.625 9.375 L 15.832031 9.375 C 16.179688 9.375 16.457031 9.65625 16.457031 10 C 16.457031 10.34375 16.179688 10.625 15.832031 10.625 L 10 10.625 C 9.65625 10.625 9.375 10.34375 9.375 10 L 9.375 4.167969 C 9.375 3.820312 9.65625 3.542969 10 3.542969 Z M 10 3.542969  "},eye:{path:"M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"},error:{path:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2zm0-4h-2V7h2z"},dropdown:{path:"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"},deleted:{path:"M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12C4,13.85 4.63,15.55 5.68,16.91L16.91,5.68C15.55,4.63 13.85,4 12,4M12,20A8,8 0 0,0 20,12C20,10.15 19.37,8.45 18.32,7.09L7.09,18.32C8.45,19.37 10.15,20 12,20Z"},microphone:{size:"large",path:"M432.8,216.4v39.2c0,45.2-15.3,84.3-45.2,118.4c-29.8,33.2-67.3,52.8-111.6,57.9v40.9h78.4c5.1,0,10.2,1.7,13.6,6c4.3,4.3,6,8.5,6,13.6c0,5.1-1.7,10.2-6,13.6c-4.3,4.3-8.5,6-13.6,6H157.6c-5.1,0-10.2-1.7-13.6-6c-4.3-4.3-6-8.5-6-13.6c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6H236v-40.9c-44.3-5.1-81.8-23.9-111.6-57.9s-45.2-73.3-45.2-118.4v-39.2c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6s10.2,1.7,13.6,6c4.3,4.3,6,8.5,6,13.6v39.2c0,37.5,13.6,70.7,40,97.1s59.6,40,97.1,40s70.7-13.6,97.1-40c26.4-26.4,40-59.6,40-97.1v-39.2c0-5.1,1.7-10.2,6-13.6c4.3-4.3,8.5-6,13.6-6c5.1,0,10.2,1.7,13.6,6C430.2,206.2,432.8,211.3,432.8,216.4z M353.5,98v157.6c0,27.3-9.4,50.3-29,69c-19.6,19.6-42.6,29-69,29s-50.3-9.4-69-29c-19.6-19.6-29-42.6-29-69V98c0-27.3,9.4-50.3,29-69c19.6-19.6,42.6-29,69-29s50.3,9.4,69,29C344.2,47.7,353.5,71.6,353.5,98z"},"audio-play":{size:"medium",path:"M43.331,21.237L7.233,0.397c-0.917-0.529-2.044-0.529-2.96,0c-0.916,0.528-1.48,1.505-1.48,2.563v41.684   c0,1.058,0.564,2.035,1.48,2.563c0.458,0.268,0.969,0.397,1.48,0.397c0.511,0,1.022-0.133,1.48-0.397l36.098-20.84   c0.918-0.529,1.479-1.506,1.479-2.564S44.247,21.767,43.331,21.237z"},"audio-pause":{size:"medium",path:"M17.991,40.976c0,3.662-2.969,6.631-6.631,6.631l0,0c-3.662,0-6.631-2.969-6.631-6.631V6.631C4.729,2.969,7.698,0,11.36,0l0,0c3.662,0,6.631,2.969,6.631,6.631V40.976z",path2:"M42.877,40.976c0,3.662-2.969,6.631-6.631,6.631l0,0c-3.662,0-6.631-2.969-6.631-6.631V6.631C29.616,2.969,32.585,0,36.246,0l0,0c3.662,0,6.631,2.969,6.631,6.631V40.976z"}}}},computed:{svgId(){const e=this.param?"-"+this.param:"";return`vac-icon-${this.name}${e}`},size(){const e=this.svgItem[this.name];return"large"===e.size?512:"medium"===e.size?48:24}}},O=B,D=(0,u.A)(O,E,L,!1,null,null,null),N=D.exports,U={name:"Carousel",components:{SvgIcon:N},props:{show:{type:Boolean},close:{type:Function,default:()=>({})},images:{type:Array,default:()=>[]}},emits:["open-forward-modal"],data(){return{activeImage:0}},computed:{currentImage(){return this.images[this.activeImage]?.big},senderName(){return this.images[this.activeImage]?.username},imageName(){return this.images[this.activeImage]?.name},imageTime(){return this.images[this.activeImage]?.date+" at "+this.images[this.activeImage]?.timestamp}},methods:{nextImage(){let e=this.activeImage+1;e>=this.images.length&&(e=0),this.activateImage(e)},prevImage(){let e=this.activeImage-1;e<0&&(e=this.images.length-1),this.activateImage(e)},activateImage(e){this.activeImage=e},async openFile(){const e=await fetch(this.currentImage),t=await e.blob(),s=document.createElement("a");s.href=URL.createObjectURL(t),s.download=this.imageName,s.click(),URL.revokeObjectURL(s.href)}}},j=U,P=(0,u.A)(j,F,R,!1,null,null,null),H=P.exports,V=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],staticClass:"error-modal"},[t("transition",{attrs:{name:"vac-bounce"}},[e.show?t("div",{staticClass:"error-modal-div"},[t("img",{attrs:{src:e.errorIcon,alt:"Error Icon"}}),t("p",[e._v(e._s(e.errorMessage))])]):e._e()]),t("div",{staticClass:"error-modal_overlay",on:{click:function(t){return t.preventDefault(),e.toggle.apply(null,arguments)}}})],1)},q=[],G=s.p+"img/error_icon.9956d282.svg",Q={name:"ErrorModal",props:{show:{type:Boolean},toggle:{type:Function,default:()=>({})},errorMessage:{type:String,default:"Something went wrong!"}},data(){return{errorIcon:G}}},z=Q,Y=(0,u.A)(z,V,q,!1,null,null,null),K=Y.exports,J=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],staticClass:"success-modal"},[t("transition",{attrs:{name:"vac-bounce"}},[e.show?t("div",{staticClass:"success-modal_content"},[t("div",{staticClass:"success-header"},[e._v(" "+e._s(e.successMessage.heading)+" "),t("button",{staticClass:"close-button",on:{click:function(t){return t.preventDefault(),e.toggle.apply(null,arguments)}}},[e._v("×")])]),t("div",{staticClass:"success-message"},[t("p",[e._v(e._s(e.successMessage.content))])])]):e._e()]),t("div",{staticClass:"success-modal_overlay",on:{click:function(t){return t.preventDefault(),e.toggle.apply(null,arguments)}}})],1)},X=[],Z={name:"SuccessModal",props:{show:{type:Boolean},toggle:{type:Function,default:()=>({})},successMessage:{type:Object,required:!0}}},W=Z,$=(0,u.A)(W,J,X,!1,null,null,null),ee=$.exports,te=function(){var e=this,t=e._self._c;return e.show?t("div",{staticClass:"image-preview"},[t("div",{staticClass:"image-preview-topbar"},[t("div",{staticClass:"image-details"},[t("span",{staticClass:"image-name"},[e._v(e._s(e.msg.username))]),t("span",{staticClass:"image-time"},[e._v(e._s(e.imageTime))])]),t("div",{staticClass:"d-flex align-items-center"},[t("div",{staticClass:"download-icon",on:{click:e.openFile}},[t("svg-icon",{attrs:{name:"document"}}),e._m(0)],1),t("div",{staticClass:"image-preview-topbar_close",on:{click:e.close}},[t("svg-icon",{attrs:{name:"close"}}),e._m(1)],1)])]),t("div",{staticClass:"image-preview-main"},[t("div",{staticClass:"content"},[t("div",{staticClass:"carousel-img"},[t("img",{attrs:{src:e.msg.url,alt:e.msg.name}})])])])]):e._e()},se=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"popover__content"},[t("p",{staticClass:"popover__message"},[e._v("Download")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"popover__content"},[t("p",{staticClass:"popover__message"},[e._v("Close")])])}],oe={name:"ImageViewer",components:{SvgIcon:N},props:{show:{type:Boolean},close:{type:Function,default:()=>({})},msg:{type:Object,required:!0}},emits:["open-forward-modal"],computed:{imageTime(){return this.msg.date+" at "+this.msg.timestamp}},methods:{async openFile(){const e=document.createElement("a");e.href=this.msg.url,e.target="_blank",e.download=this.msg.url.split("/").pop(),console.log(e.download),document.body.appendChild(e),e.click(),document.body.removeChild(e)}}},ae=oe,ie=(0,u.A)(ae,te,se,!1,null,"d17b0594",null),ne=ie.exports,re=s(2505),le=s.n(re),ce=function(){var e=this,t=e._self._c;return t("div",{staticClass:"overflow-hidden w-100 room-container"},[e.disableFooter?t("info"):e._e(),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isMobile&&!e.showRoomsList||!e.isMobile||e.singleRoom,expression:"(isMobile && !showRoomsList) || !isMobile || singleRoom"}],staticClass:"vac-col-messages",on:{touchstart:e.touchStart}},[e.showNoRoom?e._t("no-room-selected",(function(){return[t("div",{staticClass:"vac-container-center vac-room-empty"},[t("div",[e._v(e._s(e.textMessages.ROOM_EMPTY))])])]})):t("room-header",{attrs:{"current-user-id":e.currentUserId,"text-messages":e.textMessages,"single-room":e.singleRoom,"show-rooms-list":e.showRoomsList,"is-mobile":e.isMobile,"menu-actions":e.menuActions,isMsgFetched:e.isMsgFetched,room:e.room,templates:e.templates},on:{"handle-message-search":e.handleMessageSearch,"add-template":e.addTemplate,"add-template-msg":e.forwardTemplateMsg,"toggle-rooms-list":function(t){return e.$emit("toggle-rooms-list")},"toggle-menu-bar":function(t){return e.$emit("toggle-menu-bar")},"redirect-to-hubspot":function(t){return e.$emit("redirect-to-hubspot",e.room)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}),t("div",{ref:"scrollContainer",staticClass:"vac-container-scroll",on:{scroll:e.onContainerScroll}},[t("div",{staticClass:"vac-messages-container"},[t("div",{class:{"vac-messages-hidden":e.loadingMessages}},[t("transition",{attrs:{name:"vac-fade-message"}},[t("div",[e.showNoMessages?t("div",{staticClass:"vac-text-started"},[e._t("messages-empty",(function(){return[e._v(" "+e._s(e.textMessages.MESSAGES_EMPTY)+" ")]}))],2):e._e(),e.showMessagesStarted?t("div",{staticClass:"vac-text-started"},[e._v(" "+e._s(e.textMessages.CONVERSATION_STARTED)+" "+e._s(e.messages[0].date)+" ")]):e._e()])]),t("transition",{attrs:{name:"vac-fade-message"}},[e.messages.length?t("infinite-loading",{class:{"vac-infinite-loading":!e.messagesLoaded},attrs:{"force-use-infinite-wrapper":".vac-container-scroll","web-component-name":"vue-advanced-chat",spinner:"spiral",direction:"top",distance:40},on:{infinite:e.loadMoreMessages},scopedSlots:e._u([{key:"spinner",fn:function(){return[t("loader",{attrs:{show:!0,infinite:!0}})]},proxy:!0},{key:"no-results",fn:function(){return[t("div")]},proxy:!0},{key:"no-more",fn:function(){return[t("div")]},proxy:!0}],null,!1,3407458732)}):e._e()],1),t("transition-group",{key:e.roomId,attrs:{name:"vac-fade-message",tag:"span"}},e._l(e.messages,(function(s,o){return t("div",{key:s.indexId||s._id},[t("message",{attrs:{"current-user-id":e.currentUserId,message:s,index:o,messages:e.messages,"edited-message":e.editedMessage,"message-actions":e.messageActions,"room-users":e.room.users,"text-messages":e.textMessages,"room-footer-ref":e.$refs.roomFooter,"new-messages":e.newMessages,"show-reaction-emojis":e.showReactionEmojis,"show-new-messages-divider":e.showNewMessagesDivider,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"hide-options":e.hideOptions,"toggle-labels-modal":e.toggleLabelsModal,"msg-search-query":e.msgSearchQuery,"is-group":e.room.isGroup,"unread-counts":e.unreadCounts,"room-id":e.roomId},on:{"toggle-error-modal":e.toggleErrorModal,"message-added":e.onMessageAdded,"message-action-handler":e.messageActionHandler,"open-file":e.openFile,"send-message-reaction":e.sendMessageReaction,"hide-options":function(t){e.hideOptions=t},"carousel-handler":e.carouselHandler,"reply-msg-handler":e.replyMsgHandler},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)})),0)],1)])]),e.loadingMessages?e._e():t("div",[t("transition",{attrs:{name:"vac-bounce"}},[e.scrollIcon?t("div",{staticClass:"vac-icon-scroll",on:{click:e.scrollToBottom}},[t("transition",{attrs:{name:"vac-bounce"}},[e.scrollMessagesCount?t("div",{staticClass:"vac-badge-counter vac-messages-count"},[e._v(" "+e._s(e.scrollMessagesCount)+" ")]):e._e()]),e._t("scroll-icon",(function(){return[t("svg-icon",{attrs:{name:"dropdown",param:"scroll"}})]}))],2):e._e()])],1),t("div",{ref:"roomFooter",staticClass:"vac-room-footer",class:{"vac-app-box-shadow":e.shadowFooter,"footer-disabled":e.disableFooter}},[t("room-emojis",{attrs:{"filtered-emojis":e.filteredEmojis,"select-item":e.selectEmojiItem,"active-up-or-down":e.activeUpOrDownEmojis},on:{"select-emoji":function(t){return e.selectEmoji(t)},"activate-item":function(t){e.activeUpOrDownEmojis=0}}}),t("room-message-reply",{attrs:{room:e.room,"message-reply":e.messageReply,"text-formatting":e.textFormatting,"link-options":e.linkOptions},on:{"reset-message":e.resetMessage},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}),t("div",{staticClass:"vac-box-footer"},[e.showAudio||e.files.length?e._e():t("div",{staticClass:"vac-icon-textarea-left"},[e.isRecording?[t("div",{staticClass:"vac-svg-button vac-icon-audio-stop",on:{click:e.stopRecorder}},[e._t("audio-stop-icon",(function(){return[t("svg-icon",{attrs:{name:"close-outline"}})]}))],2),t("div",{staticClass:"vac-dot-audio-record"}),t("div",{staticClass:"vac-dot-audio-record-time"},[e._v(" "+e._s(e.recordedTime)+" ")]),t("div",{staticClass:"vac-svg-button vac-icon-audio-confirm",on:{click:function(t){return e.toggleRecorder(!1)}}},[e._t("audio-stop-icon",(function(){return[t("svg-icon",{attrs:{name:"checkmark"}})]}))],2)]:t("div",{staticClass:"vac-svg-button",on:{click:function(t){return e.toggleRecorder(!0)}}},[e._t("microphone-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-microphone",attrs:{name:"microphone"}})]}))],2)],2),e.showUploadModal?e._e():t("room-files",{attrs:{files:e.files},on:{"remove-file":e.removeFile,"reset-message":e.resetMessage},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}),t("div",{staticClass:"textarea-wrapper"},[t("div",{staticClass:"vac-icon-textarea"},[e.editedMessage._id?t("div",{staticClass:"vac-svg-button",on:{click:e.resetMessage}},[e._t("edit-close-icon",(function(){return[t("svg-icon",{attrs:{name:"close-outline"}})]}))],2):e._e(),e.showEmojis?t("emoji-picker-container",{directives:[{name:"click-outside",rawName:"v-click-outside",value:()=>e.emojiOpened=!1,expression:"() => (emojiOpened = false)"}],attrs:{"emoji-opened":e.emojiOpened,"position-top":!0},on:{"add-emoji":e.addEmoji,"open-emoji":function(t){e.emojiOpened=t}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}):e._e(),e.showFiles?t("div",{staticClass:"vac-svg-button paperclip-icon",on:{click:e.launchFilePicker}},[e._t("paperclip-icon",(function(){return[t("svg-icon",{attrs:{name:"paperclip",width:"19",height:"40"}})]}))],2):e._e(),e.textareaActionEnabled?t("div",{staticClass:"vac-svg-button",on:{click:e.textareaActionHandler}},[e._t("custom-action-icon",(function(){return[t("svg-icon",{attrs:{name:"deleted"}})]}))],2):e._e(),e.showFiles?t("input",{ref:"file",staticStyle:{display:"none"},attrs:{type:"file",multiple:"",accept:e.acceptedFiles},on:{change:function(t){return e.onFileChange(t.target.files)}}}):e._e()],1),t("div",{staticClass:"textarea-box"},[t("textarea",{ref:"roomTextarea",staticClass:"vac-textarea",class:{"vac-textarea-outline":e.editedMessage._id},style:{position:"relative",maxHeight:"100px"},attrs:{disabled:e.disableFooter||e.messageInTransit,placeholder:"Type Something..."},on:{input:e.onChangeInput,keydown:[function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"])?null:e.escapeTextarea.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:(t.preventDefault(),e.selectItem.apply(null,arguments))},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"tab",9,t.key,"Tab")||t.ctrlKey||t.shiftKey||t.altKey||t.metaKey?null:void t.preventDefault()},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"tab",9,t.key,"Tab")?null:e.selectItem.apply(null,arguments)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"up",38,t.key,["Up","ArrowUp"])?null:e.updateActiveUpOrDown(t,-1)},function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"down",40,t.key,["Down","ArrowDown"])?null:e.updateActiveUpOrDown(t,1)}],paste:e.onPasteImage}}),e.showSendIcon?t("div",{staticClass:"vac-svg-button send-icon",class:{"vac-send-disabled":e.isMessageEmpty},on:{click:e.sendMessage}},[e.messageInTransit?e._e():t("img",{attrs:{src:e.isMessageEmpty?e.sendIconDisabled:e.sendIcon,alt:"Send Icon",param:e.isMessageEmpty?"disabled":""}}),e.messageInTransit?t("div",{staticClass:"spinner-border text-primary",attrs:{role:"status"}},[t("span",{staticClass:"visually-hidden"},[e._v("Loading...")])]):e._e()]):e._e()])])],1)],1)],2),t("upload-modal",{attrs:{show:e.showUploadModal,files:e.files,toggle:e.toggleUploadModal,"handle-upload":e.handleUpload}})],1)},de=[],ue=(s(7642),s(8004),s(3853),s(5876),s(2475),s(5024),s(1698),s(2252)),me=s.n(ue),ge=s(1062),pe=s.n(ge),he=s(8267),fe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"loader"})},ve=[],ye={name:"Loader"},Ae=ye,be=(0,u.A)(Ae,fe,ve,!1,null,null,null),_e=be.exports,we=s.p+"img/send_icon-disabled.6a4e9ad9.svg",Ce=s.p+"img/send_icon.f3fcbb9c.svg",ke=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-emoji-wrapper"},[t("div",{staticClass:"vac-svg-button emoji-icon",class:{"vac-emoji-reaction":e.emojiReaction},on:{click:e.openEmoji}},[e._t("emoji-picker-icon",(function(){return[t("svg",{attrs:{width:"35",height:"36",viewBox:"0 0 35 36",fill:"none",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",param:e.emojiReaction?"reaction":""}},[t("rect",{attrs:{y:"0.729492",width:"35",height:"34.9353",fill:"url(#pattern0)"}}),t("defs",[t("pattern",{attrs:{id:"pattern0",patternContentUnits:"objectBoundingBox",width:"1",height:"1"}},[t("use",{attrs:{"xlink:href":"#image0",transform:"translate(0 -0.000925904) scale(0.015625)"}})]),t("image",{attrs:{id:"image0",width:"64",height:"64","xlink:href":"data:image/png;base64,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"}})])])]}))],2),e.emojiOpened?[t("transition",{attrs:{name:"vac-slide-up",appear:""}},[t("div",{staticClass:"vac-emoji-picker",class:{"vac-picker-reaction":e.emojiReaction},style:{height:`${e.emojiPickerHeight}px`,top:e.positionTop?e.emojiPickerHeight:`${e.emojiPickerTop}px`,right:e.emojiPickerRight,display:e.emojiPickerTop||!e.emojiReaction?"initial":"none"}},[e.emojiOpened?t("emoji-picker",{ref:"emojiPicker"}):e._e()],1)])]:e._e()],2)},Me=[],Ie={name:"EmojiPickerContainer",props:{emojiOpened:{type:Boolean,default:!1},emojiReaction:{type:Boolean,default:!1},roomFooterRef:{type:HTMLDivElement,default:null},positionTop:{type:Boolean,default:!1},positionRight:{type:Boolean,default:!1}},emits:["add-emoji","open-emoji"],data(){return{emojiPickerHeight:320,emojiPickerTop:0,emojiPickerRight:""}},watch:{emojiOpened(e){e&&setTimeout((()=>{this.addCustomStyling(),document.querySelector("emoji-picker").addEventListener("emoji-click",(({detail:e})=>{this.$emit("add-emoji",{unicode:e.unicode})}))}),0)}},methods:{addCustomStyling(){const e=".picker {\n\t\t\t\tborder: none;\n\t\t\t}",t=".nav {\n\t\t\t\toverflow-x: auto;\n\t\t\t}",s=".search-wrapper {\n\t\t\t\tpadding-right: 2px;\n\t\t\t\tpadding-left: 2px;\n\t\t\t}",o="input.search {\n\t\t\t\theight: 32px;\n\t\t\t\tfont-size: 14px;\n\t\t\t\tborder-radius: 10rem;\n\t\t\t\tborder: var(--chat-border-style);\n\t\t\t\tpadding: 5px 10px;\n\t\t\t\toutline: none;\n\t\t\t\tbackground: var(--chat-bg-color-input);\n\t\t\t\tcolor: var(--chat-color);\n\t\t\t}",a=document.createElement("style");a.textContent=e+t+s+o,this.$refs.emojiPicker.shadowRoot.appendChild(a)},openEmoji(e){this.$emit("open-emoji",!this.emojiOpened),this.setEmojiPickerPosition(e.clientY,e.view.innerWidth,e.view.innerHeight)},setEmojiPickerPosition(e,t,s){setTimeout((()=>{const o=t<500||s<700;if(this.roomFooterRef)if(o)this.emojiPickerRight=t/2-150+"px",this.emojiPickerTop=100,this.emojiPickerHeight=s-200;else{const t=this.roomFooterRef.getBoundingClientRect().top,s=t-e>this.emojiPickerHeight-50;this.emojiPickerTop=s?e+10:e-this.emojiPickerHeight-10,this.emojiPickerRight=this.positionTop?"-50px":this.positionRight?"60px":""}else o&&(this.emojiPickerRight="-50px")}))}}},xe=Ie,Se=(0,u.A)(xe,ke,Me,!1,null,null,null),Te=Se.exports,Fe=function(){var e=this,t=e._self._c;return e.show?t("div",[t("div",{staticClass:"upload-modal"},[t("button",{staticClass:"upload-modal_close",on:{click:function(t){return t.preventDefault(),e.toggle.apply(null,arguments)}}},[e._v("×")]),t("div",{staticClass:"upload-modal-content"},[e._m(0),t("div",{staticClass:"dragdrop-box"},[t("div",{staticClass:"drag-drop",class:{"bg-secondary":e.is_dragover},on:{drag:function(e){e.preventDefault(),e.stopPropagation()},dragstart:function(e){e.preventDefault(),e.stopPropagation()},dragend:function(t){t.preventDefault(),t.stopPropagation(),e.is_dragover=!1},dragover:function(t){t.preventDefault(),t.stopPropagation(),e.is_dragover=!0},dragenter:function(t){t.preventDefault(),t.stopPropagation(),e.is_dragover=!0},dragleave:function(t){t.preventDefault(),t.stopPropagation(),e.is_dragover=!1},drop:function(t){return t.preventDefault(),t.stopPropagation(),e.handleDragDrop.apply(null,arguments)}}},[t("img",{attrs:{src:e.dropIcon,alt:"Drag Drop Icon"}}),t("div",{staticClass:"drop-text"},[e._v("Drag and drop files here")])]),t("div",{staticClass:"diversion-text"},[e._v("OR")]),t("div",{staticClass:"upload-button"},[t("button",{on:{click:function(t){return t.preventDefault(),e.handleUpload.apply(null,arguments)}}},[e._v("Browse Files")])])]),e.files.length?t("div",{staticClass:"uploaded-files"},[e._m(1),t("div",{staticClass:"upload-file-wrapper"},e._l(e.files,(function(s){return t("div",{key:s.name,staticClass:"upload-file-item"},[t("img",{staticClass:"extension-icon",attrs:{src:"pdf"===s.extension?e.pdfIcon:e.docIcon}}),t("div",{staticClass:"file-progress-wrapper"},[t("div",{staticClass:"file-name"},[e._v(" "+e._s(s.name)+" ")]),e._m(2,!0)]),t("div",{staticClass:"status-icon"},[t("img",{attrs:{src:e.checkIcon}})])])})),0)]):e._e()])]),t("div",{staticClass:"upload-modal_overlay",on:{click:function(t){return t.preventDefault(),e.toggle.apply(null,arguments)}}})]):e._e()},Re=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"content-heading"},[t("div",{staticClass:"content-heading_main"},[e._v("UPLOAD FILES")]),t("div",{staticClass:"content-heading_sub"},[e._v("Upload documents you want to share")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"file-heading"},[t("span",[e._v("Uploaded files")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"hwa-progress-bar"},[t("div",{staticClass:"progress"},[t("div",{staticClass:"progress-bar bg-darkblue",staticStyle:{width:"100%"},attrs:{role:"progressbar","aria-valuenow":"100","aria-valuemin":"0","aria-valuemax":"100"}})])])}],Ee="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABHCAYAAAC6cjEhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAzTSURBVHgB7VwJdFTVGf5mn0ySyUISAgQFJIQlLLITqIAUdwvW6sEN9ZxWK4rFWqGlKIsUKbhgOVJURAxWoeGoIEkQBEMkKGtYQvaF7E72dTIzmaX/vS95SUwCM2GW2JPvMJw3b95y3/f++9/v//97I7HZbGvQh06QEDE29KETpOhDl+gjphvI0UtgNJrQpDfA1NwMUOeWK2RQq1Xw8lJDIpHA3fAYMXp9E7Kz8nEyKRmFBaUoKS5DTU09Ghv1sFpt0GjU0Gp90C8oACEhgRg3PgLTpo9HSP9+cAfc7nxTr2Th8DdJSEw4i+rqOkil9lkDa6ZEKsWYMcNx332zMWPWRPj6esNVcBsxpaVl2LrlUySfT+XdpkP3YC1gD+6thFQjh0Qmhc1shbXWAJvJSp6Qjm1/OB0b2M8fi59cgLvvnQ2FwvmG73Jimsln7PksHp9G74fZbG77wWyDckQgNBNCoYwIhHJIIKReLQ/ImsSIoy5lrtTDeKUcTan0OV0Cm8UqksSaPnBQKF5d/RwiRg6FM+FSYqoqa7Bq5Rakp+UKFkJ3kvmroR4fAu39EVCE+Agk2AmbyQw9kVMXn4Xm4npxv4wsbNGj92HxUwsglzvHelxGTGZGHpa/vBn19Y2tt4Jm6gAELp4Aqa9S6D49BZGsP1uMmi/SYC6qFy0oauat+Ptrz/HR7EbhEmLOnb2CTRs/REV5tXATpQz9lkzh3caZsJAPqolJReOJAv6dPcqkyWOw/o1lUKlujBynE1NcrMOSZ9agoUHPv0u1avRfHgVFmBauQl1cFmrJepjDZo8TNXMi1q5/kXexnsKpyreSfMrra7YJpDB/4qfGwH/MdSkpDNp7whH49ARI5FLuy04mnceXXxzGjcCpxETv+or7Fn5hHyVCV88mf3Lj/d0eeM+8Cf4Pj+YvhJGz4/19JA3S0FPcEDHMbE2mZjQ1GXDpUjriYo/zRklkEgT9cRJkgV5wJ3zvGE4OfiAnh8mELW/vIhVtRU/g8NiWnpaDb4/8gJycQpTpKmA0mGAmbWEyNsNqERqhHBoA5c3+wmjh5qSG/8NjYMyogqXOgKLCn5Bw7DRu//V0OAq7nK/FbMGez+MQH5fIb2aXUyNx5jNnKHzvD4ci2HXSvSs0nixE5fazXDGHhQ3AzugNDjviaxJjJkJOnkimoXcHBX36NhnfcobEWwEpuyHtt9GxVn1zm2ptvYFKDu294fBbEOE262Ft0a1LhKmglsdif135DObNj3LoGt12JdY339+2BzH/jSe2ZQIpZAXyUB94zwiDamQQFAN9ISVy2G/WpmY0l+lhLqlD44/FMJCEh8UGm9GM+kPZ0N41nOsZd0Ail5G1DkHVJxfAXMyxY6ecQwxzXOtWv4ekE+c5KexNK272g/8DI6EmkdZVfkTqo4KKfYYFwPtXQ9BcWofavaloyqzgI4ZEJXOrv9FMD0Pt/nQSgUZcTE7jKQ1/f1+7z++SmE0bP0LS9+cpzBfiG+3dw6GlriD1UsAuUHdShPoi6MWpZElmwVLc7ISZJStv8kfTZR3leJqQm1OAiZPG2H/+z3cwLXL0yEmBFIIfWYn/I5H2k9IeZFlSjYILL7eDXoR6bAjflFIe5+yZFEfO7khMfn4JPv7oixZ/QkPfg6Pgt3Ck29+2s6AeHSy2vaSkzJFT24ixkWPd8tYuMaPmPWswtL+JwC8ZsmANHwAYdD9VOiT2RGLOnL6MlMtZwk5KGPn9dtQv1lJaIVMrxEdgOWY2qNgLkZjjCadFRn3vHA55kAa/ZFgNZjT8UEj+TegB5WVV5D/38wDTHsvhAo/FOw8uWMpZZVpl4Nt3eoYYntolscjec0+slc5vLqxD3TfZaEwsEPe1ByMlINAf8++IwkMP34Wg4IAuL8UtprhIR5m2Br5DMVgLuZslPAc9QAM9TNmWH9B0rgSOw4YGSljpXj+Oxu8LBEJaYzVbC9E2YYSqralDzN54LHl2DU79eKHLq3Edk5KSyU9gUI8JcSgP6xQwUo7koir6Io9vDJfKEPj7ifCOCrPTcmyU5kxH3YGMtktSqKIaFQQ1fVg+yEoKvDm/DsasKhgyKvjIy/JHr7z0JlavW9JJGXNiiovKRDUr83NvqoCRUrs/A/WxWUKZhMAqAVXR7E3aiJzB1yWn6aIOdQczhS9Mv4zsB/9FkTzK74Cp7NqUKsmpQuVHyTD/1AAFic83N3/MyzG3ThwtHsrNhKUOxB1KN4ox4qH+cC5q96XyN9oeNlLMVTuSqVuVdvITP4fNaBGGZfrnM38YgpfP7ExK6y0pV6Qa0Q8D1s3h8R47x0R1rtfXboOeFHIrOAueqA2zoL7haB6qdwvdp4NVtGwzyyl/7wz0p4queS2WnOr33CQEPDaWfyR2pBhY1B/80jQoBgnxUw1VRfd8Hiv+zq+g9mpLP1pNFrgczKccJZ+y64JIitf4/pCohdBNQlarGhMsHEvkVGw/dx2HLIH39MGUwbvFoZcsJZ0T8Ng4Lm7ZeXEHE0nrCJbLiRl2Sxha0zJmXSNcjYbvrqI6+pJICvMjLJnd1mIgmMotoqSnhld8eF5IZTgZ6sgQCjb9+HZFRTXycgtbmwCMCB8ijkqGFF2HRJPTQQ/ZkJDfZimUxgh8YjwkPwtSWTI9iMhRhgfw42xMsH2Xd11/4zDIILzaBZvnqbbOt9l/A8NC4B8glDhYrdiUVw2XgQjRsGGYateaSQMRvGwaJJqu82WMnP4rZtEoE8Sjfe8Z1x+hegKZvzASM3so1wnPzlvE6r1z5k7Dl/sO81/Z8Mka7KpYSUu+wHfeMEGuX+ceEoUMIStnuTRuk0jbnLWlJVwQ9yx8YB58WuabNF0ohSGtAq4EGzbtflgX602rvk2u+PgI1iMSEzY4FNNnjBcbUvPZZdj0Zvzfg96P8arQfdgANHKUMJ2kw4C/7OUn4e0tBI8sw171STLPuLulfdLW4IY52p4VyXoCS70JTck6vq1UKhA5VshBdSCGTQRcvvIPVEcSGsay/VU7KdPe7HpymC+Rs+khDGzCUFUTXA6mvKmC0dpXp0yNFBPmnSTirFkT8ezzi/gEQXZiY1IhKraehs3Vwo/uJYo68j/1B7PgapgK6oiYHL7NZNxd98wWf+tSOz/y6L18dhIvnVCDDZd0KFnxLZfmTCW6Ct7TwiBVCLWnhqQCGLOr4Co0F9WhnFIcra7itjlTMCOqTWResxK55z+x2L37AAxUtOegI1kApr1/BFeMDo0s9oAuV7HlFPTJpfyrLMALwS+QyBse6Lz78F5QgJqYNFiqhe7KZn9+uHM9gkMC2w67Xu069Uo21q/9N3S6dsM362UU16gjg6EaEsCTzjJfUq4q++YIsFhIHqCBXKvqlPphkXLJiiOw1LS+DFKmtw7gH3moN+9mjoNygvVUKS1thP7HQpiu1ogpDjaXeNNbryB8xJCObbSnqG80GLEv5jC+PnAMZbrKzoFaS3bModfKJhZR+pQFj6y23T5ryPIk5f86JUxAbH8ra8uJPYGk85TY4eE347U1z3Op0ulwR6aaVVfXIjHhDOJjE5GRcVW4lxPiKlbevf2RuXjgd/PFfWYy84SYRBz46iifBuuU1IiNDXg2hIYGYf6dUXhi8QJKVHVdSOzxHDyWdWcp0cyMfJ7LMJqM5Jjt0x9GkxkVZdUoKCgVSxpMIjz59EI88dRCClHaiv9s9jiroV9JyYLBYOhh2lUChULBs3QTJkRg3ISR0Giunan02Hollq3PyszHjg9icO5sCrcIM5GzZt3zmDvP8Yk+zkavWMi1ccMHOHzoBCdHrVbj6/jtdq8xcBV6xXqlF158HKNG38K32Xy+gwe+g6fRK4jx8dHgoUV3c7XNfKyjMxNcgV6zwi0yMhwqpaCDcnPy4Wn0GmLYwi1Zy/Ka6pp6eBq9ak2kpG29DTyNvsWi3cAjxGRn52P7tr24eCHdruNPn7qIT6MPoKysEu6C2xeLsl7yzlufID01B3up8rd2/VLcNntKt8cfivseW9/dTarXiLy8Iry6egncAY/MGhwQKiSkmIhb8+pWJB4/0+WRsV8n4O03d3JSmA4dNCgE7oLbiWHqdumyxxERMVRcKbJ54w4cik/sECjGxSaQZe2CxSKsQZp122Q8TkGf29rpqZCAReorV7yDzHShusgS0a2ra4V5/xIixcJJYfNzN/zzz/wYd8GjsRKb4rZs6QZkpOd2e8yUqWOxivwKU8fuhEeHa2YBb2x6uVP2rBVTp43zCCkMvSK6rq1twIq/bKY0xFVx3/QZE/C3Vc96hBSGXvP3Yxg5q1e9SwmpbEyeHIl1G/7kkhX49qJX/WEd1hSWGWRTTKVSz4ryvr841A36YqVu0EdMN2DebS360An/A1Z9RlTCormHAAAAAElFTkSuQmCC",Le="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAzCAYAAADVY1sUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANLSURBVHgB7ZpPSBRRHMe/v2eumRlaSdhquppoRWVgEHQp6RTowYggRDK62aGgDlmZJYSHqA5BnYIE8yRC3kKkS9L/IqFS8r+tiof8k6nrzvx6M9OuWS3B7INmlv0cZt785rG87/7ee/N7v/cIEeCBgTR4RAkE7YbOqfifEAUQxCto2gvy+ab+WuV3A3/pL4Tw1IL1Y/IxCc5Cl6KasRCsk4IGf32xQgiPjZwGc4MspsHR0Kxs5zXybrkRtoQKUsQV+bIeboJEHWVmNZhF48L+wQpAtMKNaNpByvY9IWYWGBvpk6ZcuJNR9PT5iMdGq+TAfgBXw1XCuMD1UIWQA9wH91MiPYIYEEKbDSEE18NCIEaIGSGrEA1jI9Bb74P7e6R3GbYgAnlzQZU1oE1e2IXYP2yvBTNfoTWeB+bnoISUVCRcvA0kr4EdbHct/vRenQiDuVnob57CLvaFfJuBchYXYJf4rOU04kKcRlyI04gLcRpxIU4juug3hBHB7txrlWenZLq11zJv3W4Gg0bowcOfge9zVt28ImDtOiCwaMVsrCNalAihomKI6rPhZ72tCdzVAVFzOWzjkQHoN2tB5ZUQBw4v1717HdzbjWhR4xGPx7zx6y7phSKI/YegPeu0bD2ykYmJlhcSPaAde8Af34EfNcvkWhA8OQ4VKB0jVLANSE0DT/iXjQvzsrtN/6wg0wMJq8wFFB2pBh09BaRvgArUeCSEHAPG+NDb/pHvS06Rl0lgKWDkb6ECpUL09ofgD2+tB4+1I0G5BWaXkp3MarhR6n4JveUeVKLm7xgflfsqQ2B5DxNcMu1ISDBnJ73pjrWuH+ozMv9Qje01u97ZDpYeUNqYsuMQpWWwg32PJK2Gashm4sHAthBRvC88DpRgTNGFu2AX+x6RX2xx4gxofQaihdI3Qpw8B0TxW/bzWg4jHv06DUNIzHStCbgeHjSEPIfrISmE8Bhuh9FEPDmZisD8sBTk8GMbERnE/FKxoIyMWemaGrgVEvWUnz9tTr/kzZbxN1+F22BupMwsc/ET/o6QN6feRWIYul4n23whZPjzvNbQUB48uAWmcjiTDmjaJcr2rZhtI+6xs9+fAw6WylIOnICgCSxoLZFO0P0AZMUbQ0m91ZkAAAAASUVORK5CYII=",Be="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAzCAYAAADVY1sUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAANRSURBVHgB7ZpLSBVRGMf/31wf92aaJm3UykcPMltIBEFUVGCFUG1a9KBFm6BVq4hKtEVQix5QRC2ijRC4tII2QSTRRrJFhabXDKPUNNTMe+d675z+507mNXUzd8AZuT8Y5pxvzjDzP+c753znzAgWQKmOQkTzaxFADVSgCFbcwGIhMgElHQiiXaRqbN4i/xuUCq+BqRp483Fml8FTSIxv/BgJs0lCm/pmXUnNqFjPGSq/xmQhvM0YFJokWHV72vDPXdRUbwNF3If3RWhWsAluKbP34rQh2SIqGq5n6in8iBXfJaGNbYZSimLUXfgVI+uR1iDKDJ9gthm+Rh1jH5GT8DsKRw2IKoffEakVFe2ZYiIL/iZmLAERmpzFCztcZskISc+tol3Aj0Yg0q6DNDiD01juZqDkBh1kHZyi5xFnbxAfAvr2A4lxuEJgJVD5gj5SACc4d63fr9wToUn8BMafwCnOhegHu401CadkRi2vkRHiNTJCvEZGiNfICPEa7iyqhBFsXh2S9ZIYZTT8xrYbedyr3MHQwwTMd7w2ZpcNbmOQWMyIOQJMtvGcQLq4IyRvN1B6byY/wvQww/K1rQzNy21b9D3w5TCwintqRadnyn49xQD0NdLFHdeSkH1ORsTDQMEh2nJtEeYHHp1shRo+LUjRe1nuJZcAB4DP+1wRoXF3va6jVytmp+VvHSUmUgrQrfQWQXyE4rrhJpnOPi+hrayafNZ2l97a1zvjdKlq2FvMXIhaUbbGIAeA7exTD2mzgMELdguliTstot3EDNvuFPtkv5weiQbO2y9u0b2+n0NSzFAT878oegvX6htm+leauNMisR523rq59vFW+0gl+pFl6+E2zltEzxFuYyyHU5wLyT/Iu138MqeH67ydcIpzIXr7puQOkF2KtMkuYed/wHMZnOJ8X8tjZKJfr2FwzzYO/5PQLTIA/xM2EJB2+B2l+vnFKvAMfkehWSgmhJjZz5G4GP6kDzk51YbIaq431Vn4FUsatIbk8Cu561tgWVfgNyx1VUKVyZ8dZv8dFAk3cq7nIQIvo6cMkcuSW3V92jT3f61IZwWM7JtMHoEnUc+Rk3VJpPxtqnXBmleT3WW8uofJKngC+YZgoEWkYnS+q38Aysn9yr0m94EAAAAASUVORK5CYII=",Oe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAQCAYAAADJViUEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFCSURBVHgB3ZG/S8NAFMffuySkKI1S6+TikkXHTg6S6m5xCF2cXAQnpV1cRCfxTxAcHJxMBKGLi2JxchAEFQoibu3kpJAmqPd819L0BxSytl+43/d5977vAEZCeTrUc/RodNYiKZirVyYwWNxKhx/nq4E3p/b0pLA1Ex3wsIMEJgE2eF5KBDuBV1IgEJncGpoGZ31pL5GXUZ4GwXzTPxaIRwokSTUS6NyYxZcYdpoXK2YIDxgtnChvMRj4ZUTYBWiDKHC7miq+d87bLxGs8yWbR9vKhGkOsDmdjfakpH3eU6rDLxbuprqgEqpu+dub1TXweeW0Ykl6RU3YrVQBalKKjftJ92nQEsYpflWyaISXykV8ysVRHntT7VVcsKpV+KSflMvfcM0x/5h8Q6K1YWDfy93qXs1LisqGpp/emu4zjJ/+AW8QePfX4MASAAAAAElFTkSuQmCC",De={name:"UploadModal",props:{show:{type:Boolean},files:{type:Array,default:[]},toggle:{type:Function,default:()=>({})},handleUpload:{type:Function,default:()=>({})}},data(){return{dropIcon:Ee,pdfIcon:Le,docIcon:Be,checkIcon:Oe,is_dragover:!1}},methods:{handleDragDrop(e){this.is_dragover=!1,this.handleUpload(e)}}},Ne=De,Ue=(0,u.A)(Ne,Fe,Re,!1,null,null,null),je=Ue.exports,Pe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"info-modal"},[t("transition",{attrs:{name:"vac-bounce"}},[t("div",[t("img",{attrs:{src:e.infoIcon,alt:""}}),t("span",[e._v(e._s(e.infoMsg))])])])],1)},He=[],Ve=s.p+"img/info-Icon.bccac46c.svg",qe={data(){return{infoIcon:Ve}},props:{infoMsg:{type:String,default:"Outside 24-hours window. Please send a template to initiate conversation."}}},Ge=qe,Qe=(0,u.A)(Ge,Pe,He,!1,null,null,null),ze=Qe.exports,Ye=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-room-header"},[e._t("room-header",(function(){return[t("div",{staticClass:"vac-room-wrapper"},[t("div",{staticClass:"vac-info-wrapper"},[e._t("room-header-avatar",(function(){return[t("div",{staticClass:"vac-avatar",style:{"background-image":`url('${e.room.avatar||e.dummyAvatar}')`}})]}),null,{room:e.room}),e._t("room-header-info",(function(){return[t("div",{staticClass:"vac-text-ellipsis"},[t("div",{staticClass:"vac-room-name vac-text-ellipsis"},[e._v(" "+e._s(e.userName)+" ")])])]}),null,{room:e.room,typingUsers:e.typingUsers})],2),t("div",{staticClass:"d-flex align-items-center"},[t("div",{staticClass:"hwa-template"},[t("div",{staticClass:"external-link"},[t("a",{attrs:{href:`${e.config.baseURL}dashboard/?user_id=${e.userId}&accountUser=${e.accountUser}&portal_id=${e.portalId}&accountPhone=${e.accountPhone}`,target:"_blank"}},[t("img",{attrs:{src:e.ExternalIcon,alt:""}})]),t("span",{staticClass:"tooltip-span"},[e._v("Go To Main DashBoard")])]),t("div",{staticClass:"template-box",on:{click:function(t){return t.preventDefault(),e.templateHandler.apply(null,arguments)}}},[t("p",{staticClass:"hwa-template_text"},[e._v("Templates")]),t("img",{class:{rotateSvg:e.templateOpened},attrs:{src:e.DropDownIcon,alt:"",srcset:""}})]),t("transition",{attrs:{name:"vac-slide-left"}},[e.templateOpened?t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.closeTemplates,expression:"closeTemplates"}],staticClass:"vac-menu-options"},[e.localTemplates.length?e._e():t("div",{staticClass:"vac-menu-item no-template-center"},[t("a",{staticClass:"nav-link",attrs:{target:"_blank",href:"https://business.facebook.com/wa/manage/message-templates"}},[e._v(" Create Template ")])]),e.localTemplates.length?t("div",[t("div",{staticClass:"template-search-box"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.searchTemplate,expression:"searchTemplate"}],attrs:{type:"text",placeholder:"Search Template"},domProps:{value:e.searchTemplate},on:{input:[function(t){t.target.composing||(e.searchTemplate=t.target.value)},function(t){return e.debouncedHandleKeypressWhatsApp(e.searchTemplate)}]}}),t("img",{staticClass:"template-search-image_icon",attrs:{src:e.searchIcon,alt:"search icon"}})]),t("ul",{staticClass:"hwa-menu-list"},[e.loading?t("div",{staticClass:"vac-menu-item loading-item"},[e._v("Searching templates...")]):e._e(),e._l(e.filteredTemplates,(function(s){return t("li",{key:s.id,staticClass:"vac-menu-item",attrs:{title:s.name},on:{click:function(t){return t.preventDefault(),e.openModal(s)}}},[e._v(" "+e._s(s.name)+" ")])})),e.showNoDataMessage&&!e.loading?t("li",{staticClass:"no-data-found"},[e._v("Template not found")]):e._e()],2)]):e._e()]):e._e()])],1)])])]}),null,{room:e.room,typingUsers:e.typingUsers}),e.isModalOpen?t("template-modal",{attrs:{selectedTemplate:e.selectedTemplate,roomPhone:e.room.roomId,objectId:e.room.object_id,defaultHeaderTemplateUrl:e.defaultHeaderTemplateUrl},on:{"close-modal":e.closeModal,"add-template-msg":e.forwardTemplateMsg}}):e._e()],2)},Ke=[],Je=s.p+"img/refresh-msg.5335dbf4.svg",Xe=s.p+"img/external-link.4f7045a4.svg",Ze="data:image/png;base64,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",We=s.p+"img/dropdown.4e25eebb.svg",$e=s.p+"img/searchicon.65130997.svg",et=(e,t,s)=>{if(e.typingUsers&&e.typingUsers.length){const o=e.users.filter((s=>{if(s._id!==t&&-1!==e.typingUsers.indexOf(s._id)&&(!s.status||"offline"!==s.status.state))return!0}));if(!o.length)return;return 2===e.users.length?s.IS_TYPING:o.map((e=>e.username)).join(", ")+" "+s.IS_TYPING}},tt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"template-modal"},[t("div",{staticClass:"modal-overlay",on:{click:e.closeModal}}),t("div",{staticClass:"modal-dialog modal-md"},[t("div",{staticClass:"modal-content"},[t("div",{staticClass:"modal-header justify-content-between"},[t("h5",{staticClass:"modal-title"},[e._v("Send template message")]),t("button",{staticClass:"btn",attrs:{type:"button"},on:{click:e.closeModal}},[t("img",{attrs:{src:e.closeIcon,alt:"",srcset:""}})])]),t("hr"),t("form",{on:{submit:function(t){return t.preventDefault(),e.submitHandler.apply(null,arguments)}}},[t("div",{staticClass:"modal-body"},[t("p",[t("span",{staticClass:"fw-bolder"},[e._v("Template Name: ")]),e._v(e._s(e.selectedTemplate.name))]),t("p",[t("span",{staticClass:"fw-bolder"},[e._v(" Language Code: ")]),e._v(e._s(e.selectedTemplate.language))]),t("p",{staticStyle:{"white-space":"pre-wrap"}},[t("span",{staticClass:"fw-bolder"},[e._v("Template Content: ")]),e._v(e._s(e.selectedTemplate.body)+" ")]),e.selectedTemplate?t("div",{staticClass:"mt-5"},[e.selectedTemplate.hasHeaderParam?[t("div",{key:e.selectedTemplate.id,staticClass:"form-group"},[t("div",{staticClass:"d-flex justify-content-between align-items-center"},[t("label",{staticClass:"form-label"},[e._v("Header Param Text")]),t("div",{staticClass:"dropdown"},[t("button",{staticClass:"btn tokenButton dropdown-toggle",attrs:{type:"button","data-bs-toggle":"dropdown"},on:{click:function(t){return t.preventDefault(),e.tokenModalHandler("header_text_1")}}},[e._v(" Contact Tokens ")]),"header_text_1"===e.activeTokenModal?t("ul",{staticClass:"token-dropdown"},[t("li",[t("input",{staticClass:"form-control search-input",attrs:{type:"text",placeholder:"Search property name"},on:{keyup:function(t){return e.searchProperties(t)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}})]),t("div",{staticClass:"dropdown-data"},[e._l(e.properties,(function(s){return t("li",{key:s.name,staticClass:"tokenItem",attrs:{"data-name":s.label}},[t("button",{staticClass:"dropdown-item",attrs:{title:s.name,type:"button"},on:{click:function(t){return e.addToken(s.name,"header_text_1")}}},[e._v(" "+e._s(s.label)+" ")])])})),t("p",{staticClass:"no-data-found"},[e._v("Contact token not found")])],2)]):e._e()])]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.templateFields["header_text_1"],expression:"templateFields['header_text_1']"}],staticClass:"form-control",attrs:{name:"header_text_1",type:"text",placeholder:"Enter value",required:""},domProps:{value:e.templateFields["header_text_1"]},on:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()},input:function(t){t.target.composing||e.$set(e.templateFields,"header_text_1",t.target.value)}}})])]:e._e(),0!==e.selectedTemplate.params?e._l(e.selectedTemplate.params,(function(s){return t("div",{key:e.selectedTemplate.id+s,staticClass:"form-group"},[t("div",{staticClass:"d-flex flex-column"},[t("div",{staticClass:"dropdown d-flex justify-content-between"},[t("label",{attrs:{for:"placeholder_id_"+s}},[e._v("Placeholder "+e._s(s))]),t("button",{staticClass:"btn tokenButton dropdown-toggle",attrs:{type:"button","aria-expanded":"true"},on:{click:function(t){return t.preventDefault(),e.tokenModalHandler(`placeholder${s}`)}}},[e._v(" Contact Tokens ")]),e.activeTokenModal===`placeholder${s}`?t("ul",{staticClass:"token-dropdown"},[t("li",[t("input",{staticClass:"form-control search-input",attrs:{type:"text",placeholder:"Search property name"},on:{keyup:function(t){return e.searchProperties(t)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}})]),t("div",{staticClass:"dropdown-data"},[e._l(e.properties,(function(o){return t("li",{key:o.name,staticClass:"tokenItem",attrs:{"data-name":o.label}},[t("button",{staticClass:"dropdown-item",attrs:{title:o.name,type:"button"},on:{click:function(t){return e.addToken(o.name,"placeholder_"+s)}}},[e._v(" "+e._s(o.label)+" ")])])})),t("p",{staticClass:"no-data-found"},[e._v("Contact token not found")])],2)]):e._e()]),t("input",{directives:[{name:"model",rawName:"v-model",value:e.templateFields[`placeholder_${s}`],expression:"templateFields[`placeholder_${index}`]"}],staticClass:"form-control",attrs:{id:"placeholder_id_"+s,name:"placeholder_"+s,type:"text",placeholder:"Enter placeholder value ",required:""},domProps:{value:e.templateFields[`placeholder_${s}`]},on:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()},input:function(t){t.target.composing||e.$set(e.templateFields,`placeholder_${s}`,t.target.value)}}})])])})):e._e(),e.matchedDependency?[e._l(e.matchedDependency.dependentFieldNames,(function(s,o){return[t("div",{key:o,staticClass:"form-group"},[t("div",{staticClass:"d-flex justify-content-between align-items-center"},[t("div",{staticClass:"info-icon-div"},[t("label",{staticClass:"form-label"},[e._v(e._s(Object.values(s)[0]))]),Object.keys(s)[0].endsWith("_url")?t("div",{staticClass:"info-hint"},[t("img",{attrs:{src:e.infoIcon,alt:"info Icon"}}),e._m(0,!0)]):e._e()]),t("div",{staticClass:"dropdown"},[t("button",{staticClass:"btn tokenButton dropdown-toggle",attrs:{type:"button","data-bs-toggle":"dropdown","aria-expanded":"false"},on:{click:function(t){t.preventDefault(),e.tokenModalHandler(Object.values(s)[0])}}},[e._v(" Contact Tokens ")]),e.activeTokenModal===Object.values(s)[0]?t("ul",{staticClass:"token-dropdown"},[t("li",[t("input",{staticClass:"form-control search-input",attrs:{type:"text",placeholder:"Search property name"},on:{keyup:function(t){return e.searchProperties(t)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}})]),t("div",{staticClass:"dropdown-data"},[e._l(e.properties,(function(o){return t("li",{key:o.name,staticClass:"tokenItem",attrs:{"data-name":o.label}},[t("button",{staticClass:"dropdown-item",attrs:{title:o.label,type:"button"},on:{click:function(t){e.addToken(o.name,Object.keys(s)[0])}}},[e._v(" "+e._s(o.label)+" ")])])})),t("p",{staticClass:"no-data-found"},[e._v("Contact token not found")])],2)]):e._e()])]),t("input",{staticClass:"form-control",attrs:{name:Object.keys(s)[0],type:"text",placeholder:"Enter value",required:""},domProps:{value:e.tokenValue||e.defaultUrl||""},on:{input:function(t){e.updateField(t,Object.keys(s)[0])},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}})])]}))]:e._e()],2):e._e()]),t("div",{staticClass:"modal-footer footer"},[t("button",{staticClass:"btn btn-primary",attrs:{disabled:this.apiLoader,type:"submit"}},[e._v("Send")]),this.apiLoader?t("Loader"):e._e()],1)])])]),t("error-modal",{attrs:{show:this.sendtemplateError,toggle:e.hideErrorModal,"error-message":this.errorMessage}})],1)},st=[function(){var e=this,t=e._self._c;return t("p",[e._v("Please refer to the Meta guidelines "),t("a",{attrs:{href:"https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media/#supported-media-types",target:"_blank"}},[e._v("here")]),e._v(" in respect of supported media type and maximum file size")])}];const ot={baseURL:"https://vira.niswey.net/"};var at=ot,it=le().create({baseURL:at.baseURL}),nt=JSON.parse('[{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_url":"Document URL"}],"controllingFieldName":"media_type","controllingFieldValue":"document"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_url":"Image Url"}],"controllingFieldName":"media_type","controllingFieldValue":"image"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_url":"Video url"}],"controllingFieldName":"media_type","controllingFieldValue":"video"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_address":"Location address"},{"location_name":"Location name"},{"latitude":"Latitude"},{"longitude":"Longitude"}],"controllingFieldName":"media_type","controllingFieldValue":"location"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"buttons_dynamic_1_button_1":"Button 1"}],"controllingFieldName":"media_type","controllingFieldValue":"buttons_dynamic_1"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"button_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"button_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"buttons_dynamic_2_button_1":"Button 1"},{"buttons_dynamic_2_button_2":"Button 2"}],"controllingFieldName":"media_type","controllingFieldValue":"buttons_dynamic_2"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"buttons_dynamic_1_copy_button_1":"Button 1"},{"buttons_dynamic_1_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"buttons_dynamic_1_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"buttons_dynamic_2_copy_button_1":"Button 1"},{"buttons_dynamic_2_copy_button_2":"Button 2"},{"buttons_dynamic_2_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"buttons_dynamic_2_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_buttons_dynamic_1_url":"Image button url"},{"image_buttons_dynamic_1_button_1":"Button 1"}],"controllingFieldName":"media_type","controllingFieldValue":"image_buttons_dynamic_1"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_buttons_dynamic_2_url":"Image button url"},{"image_buttons_dynamic_2_button_1":"Button 1"},{"image_buttons_dynamic_2_button_2":"Button 2"}],"controllingFieldName":"media_type","controllingFieldValue":"image_buttons_dynamic_2"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_buttons_copy_url":"Image button url"},{"image_buttons_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"image_buttons_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_buttons_dynamic_1_copy_url":"Image button url"},{"image_buttons_dynamic_1_copy_button_1":"Button 1"},{"image_buttons_dynamic_1_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"image_buttons_dynamic_1_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"image_buttons_dynamic_2_copy_url":"Image button url"},{"image_buttons_dynamic_2_copy_button_1":"Button 1"},{"image_buttons_dynamic_2_copy_button_2":"Button 2"},{"image_buttons_dynamic_2_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"image_buttons_dynamic_2_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_buttons_dynamic_1_url":"Video button url"},{"video_buttons_dynamic_1_button_1":"Button 1"}],"controllingFieldName":"media_type","controllingFieldValue":"video_buttons_dynamic_1"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_buttons_dynamic_2_url":"Video button url"},{"video_buttons_dynamic_2_button_1":"Button 1"},{"video_buttons_dynamic_2_button_2":"Button 2"}],"controllingFieldName":"media_type","controllingFieldValue":"video_buttons_dynamic_2"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_buttons_copy_url":"Video button url"},{"video_buttons_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"video_buttons_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_buttons_dynamic_1_copy_url":"Video button url"},{"video_buttons_dynamic_1_copy_button_1":"Button 1"},{"video_buttons_dynamic_1_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"video_buttons_dynamic_1_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"video_buttons_dynamic_2_copy_url":"Video button url"},{"video_buttons_dynamic_2_copy_button_1":"Button 1"},{"video_buttons_dynamic_2_copy_button_2":"Button 2"},{"video_buttons_dynamic_2_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"video_buttons_dynamic_2_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_buttons_dynamic_1_url":"Document button url"},{"document_buttons_dynamic_1_button_1":"Button 1"}],"controllingFieldName":"media_type","controllingFieldValue":"document_buttons_dynamic_1"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_buttons_dynamic_2_url":"Document button url"},{"document_buttons_dynamic_2_button_1":"Button 1"},{"document_buttons_dynamic_2_button_2":"Button 2"}],"controllingFieldName":"media_type","controllingFieldValue":"document_buttons_dynamic_2"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_buttons_copy_url":"Document button url"},{"document_buttons_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"document_buttons_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_buttons_dynamic_1_copy_url":"Document button url"},{"document_buttons_dynamic_1_copy_button_1":"Button 1"},{"document_buttons_dynamic_1_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"document_buttons_dynamic_1_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"document_buttons_dynamic_2_copy_url":"Document button url"},{"document_buttons_dynamic_2_copy_button_1":"Button 1"},{"document_buttons_dynamic_2_copy_button_2":"Button 2"},{"document_buttons_dynamic_2_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"document_buttons_dynamic_2_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_buttons_dynamic_1_location_address":"Location address"},{"location_buttons_dynamic_1_location_name":"Location name"},{"location_buttons_dynamic_1_latitude":"Latitude"},{"location_buttons_dynamic_1_longitude":"Longitude"},{"location_buttons_dynamic_1_button_1":"Button 1"}],"controllingFieldName":"media_type","controllingFieldValue":"location_buttons_dynamic_1"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_buttons_dynamic_2_location_address":"Location address"},{"location_buttons_dynamic_2_location_name":"Location name"},{"location_buttons_dynamic_2_latitude":"Latitude"},{"location_buttons_dynamic_2_longitude":"Longitude"},{"location_buttons_dynamic_2_button_1":"Button 1"},{"location_buttons_dynamic_2_button_2":"Button 2"}],"controllingFieldName":"media_type","controllingFieldValue":"location_buttons_dynamic_2"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_buttons_copy_location_address":"Location address"},{"location_buttons_copy_location_name":"Location name"},{"location_buttons_copy_latitude":"Latitude"},{"location_buttons_copy_longitude":"Longitude"},{"location_buttons_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"location_buttons_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_buttons_dynamic_1_copy_location_address":"Location address"},{"location_buttons_dynamic_1_copy_location_name":"Location name"},{"location_buttons_dynamic_1_copy_latitude":"Latitude"},{"location_buttons_dynamic_1_copy_longitude":"Longitude"},{"location_buttons_dynamic_1_copy_button_1":"Button 1"},{"location_buttons_dynamic_1_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"location_buttons_dynamic_1_copy"},{"dependencyType":"CONDITIONAL_SINGLE_FIELD","dependentFieldNames":[{"location_buttons_dynamic_2_copy_location_address":"Location address"},{"location_buttons_dynamic_2_copy_location_name":"Location name"},{"location_buttons_dynamic_2_copy_latitude":"Latitude"},{"location_buttons_dynamic_2_copy_longitude":"Longitude"},{"location_buttons_dynamic_2_copy_button_1":"Button 1"},{"location_buttons_dynamic_2_copy_button_2":"Button 2"},{"location_buttons_dynamic_2_copy_copy":"Copy button"}],"controllingFieldName":"media_type","controllingFieldValue":"location_buttons_dynamic_2_copy"}]'),rt=s.p+"img/close-icon.0e262d61.svg",lt=s.p+"img/info.08aba135.svg",ct={name:"TemplateModal",emits:["create-handler"],components:{Loader:_e,ErrorModal:K},props:{selectedTemplate:Object,roomPhone:String,tokens:String,objectId:Number,defaultHeaderTemplateUrl:String},data(){return{properties:[],templateFields:{},activeTokenModal:null,apiLoader:!1,closeIcon:rt,infoIcon:lt,sendtemplateError:!1,errorMessage:"Failed to send template, Please try again",tokenValue:"",defaultUrl:null}},computed:{matchedDependency(){return nt.find((e=>e.controllingFieldValue===this.selectedTemplate?.type))}},created(){const e=this.$store.state.userData;this.user_id=e.user_id,this.getHubspotProperties()},watch:{defaultHeaderTemplateUrl:{handler(e){if(e){let t=nt.find((e=>e.controllingFieldValue===this.selectedTemplate?.type));if(!t||!t.dependentFieldNames||0===t.dependentFieldNames.length)return;let s=Object.keys(t?.dependentFieldNames[0])[0];this.$set(this.templateFields,s,e),this.defaultUrl=e}},immediate:!0,deep:!0}},methods:{hideErrorModal(){this.sendtemplateError=!1},updateField(e,t){this.$set(this.templateFields,t,e?.target?.value),this.tokenValue=e?.target?.value,this.defaultUrl=null},tokenModalHandler(e){this.activeTokenModal===e?this.activeTokenModal=null:this.activeTokenModal=e},closeModal(){this.tokenValue=null,this.$emit("close-modal"),this.templateFields={},this.tokenModalHandler(null)},searchProperties(e){let t=e.target.value.trim().toLowerCase(),s=e.target.parentElement.parentElement.parentElement.querySelectorAll(".tokenItem"),o=document.querySelector(".no-data-found"),a=!1;if(s)for(let i=0;i<s.length;i++){let e=s[i],o=e.getAttribute("data-name").trim().toLowerCase();o.includes(t)?(e.style.display="block",a=!0):e.style.display="none"}o.style.display=a?"none":"flex"},addToken(e,t){this.templateFields[t]||this.$set(this.templateFields,t,""),this.tokenValue=`[${e}]`,this.templateFields[t]=`[${e}]`,this.defaultUrl=null,this.activeTokenModal=null},submitHandler(e){var t=window.location.href,s=new URL(t);const o=s.searchParams.get("user_id"),a=s.searchParams.get("objectId"),i=s.searchParams.get("phone");let n={phone:i,objectId:a,message:this.selectedTemplate.body,templateId:this.selectedTemplate.id,...this.templateFields&&{fields:this.templateFields}};const r={user_id:o,...n};this.apiLoader=!0;try{it.post("api/v1/send-template",r).then((e=>{this.apiLoader=!1,e.data.ok?(this.closeModal(),this.$emit("add-template-msg",e.data)):(this.errorMessage=e.data.message,this.sendtemplateError=!0)})).catch((e=>{this.apiLoader=!1,this.sendtemplateError=!0,console.error(e)}))}catch(l){this.apiLoader=!1,console.log(l)}},async getHubspotProperties(){try{const e=await it.get(`api/hubspot/properties?user_id=${this.user_id}`);this.properties=e.data.data.results}catch(e){console.error(e)}}}},dt=ct,ut=(0,u.A)(dt,tt,st,!1,null,null,null),mt=ut.exports,gt={name:"RoomHeader",components:{TemplateModal:mt},directives:{clickOutside:pe().directive},props:{currentUserId:{type:[String,Number],required:!0},textMessages:{type:Object,required:!0},singleRoom:{type:Boolean,required:!0},isMsgFetched:{type:Boolean,required:!0},menuActions:{type:Array,required:!0},room:{type:Object,required:!0},templates:{type:Array,required:!0}},emits:["add-template","handle-message-search","toggle-menu-bar","redirect-to-hubspot"],data(){return{refreshIcon:Je,dummyAvatar:Ze,DropDownIcon:We,menuOpened:!1,searchIcon:$e,templateOpened:!1,currentIdx:-1,searchTemplate:"",showNoDataMessage:!1,isModalOpen:!1,selectedTemplate:null,roomPhone:String,userName:"",ExternalIcon:Xe,localTemplates:[],loading:!1,userId:"",config:at,defaultHeaderTemplateUrl:null}},computed:{typingUsers(){return et(this.room,this.currentUserId,this.textMessages)},filteredTemplates(){const e=this.localTemplates.filter((e=>e.name.toLowerCase().includes(this.searchTemplate.toLowerCase())));return e}},created(){var e=window.location.href,t=new URL(e);let s=t.searchParams.get("firstname"),o=t.searchParams.get("lastname"),a=t.searchParams.get("phone");this.userName=s&&"null"!==s&&"undefined"!==s?o&&"null"!==o&&"undefined"!==o?`${s} ${o}`:s:o&&"null"!==o&&"undefined"!==o?o:a,this.userId=t.searchParams.get("user_id"),this.accountUser=t.searchParams.get("accountUser"),this.portalId=t.searchParams.get("portal_id"),this.accountPhone=t.searchParams.get("accountPhone"),this.localTemplates=this.templates,this.debouncedHandleKeypressWhatsApp=this.debounce(this.handleKeypressWhatsApp,300)},watch:{filteredTemplates(e){this.showNoDataMessage=0===e.length},templates:{immediate:!0,handler(e){e&&e.length&&(this.localTemplates=[...e])}}},methods:{debounce(e,t){let s;return function(...o){const a=this;clearTimeout(s),s=setTimeout((()=>e.apply(a,o)),t)}},async handleKeypressWhatsApp(e){let t=e?.toLowerCase();if(this.isMatch=!1,0===t.length)return;const s=this.localTemplates.filter((e=>e.name.toLowerCase().includes(t)));if(s.length>0&&(this.isMatch=!0),t.length>=3&&0===s.length){this.loading=!0;try{const e=await it.post("api/whatsapp/template",{user_id:this.userId,filters:{name:t,status:"APPROVED",parse:!0}});let s=e?.data?.templates?.data||[];if(s.length>0){const e=s.filter((e=>!this.localTemplates.some((t=>t.id===e.id))));e.length>0&&(this.localTemplates=this.localTemplates.concat(e),this.isMatch=!0)}}catch(o){console.error("Error fetching WhatsApp templates:",o)}finally{this.loading=!1}}},forwardTemplateMsg(e){this.$emit("add-template-msg",e)},closeMenu(){this.menuOpened=!1},closeTemplates(){this.templateOpened=!1,this.searchTemplate=""},templateHandler(){this.templateOpened=!this.templateOpened},openModal(e){this.checkTemplateHeaderUrl(e?.id),this.isModalOpen=!0,this.selectedTemplate=e,this.templateOpened=!this.templateOpened,this.searchTemplate=""},closeModal(){this.isModalOpen=!1,this.selectedTemplate=null},async checkTemplateHeaderUrl(e){this.defaultHeaderTemplateUrl=null;try{const{data:t}=await it.get(`api/template/media/${e}?user_id=${this.userId}`);this.defaultHeaderTemplateUrl=t?.file_url}catch(t){this.errorMessage="Unable to update",setTimeout((()=>this.errorMessage=""),1e3),console.log(t)}}}},pt=gt,ht=(0,u.A)(pt,Ye,Ke,!1,null,null,null),ft=ht.exports,vt=function(){var e=this,t=e._self._c;return t("transition",{attrs:{name:"vac-slide-up"}},[e.files.length?t("div",{staticClass:"vac-room-files-container",style:{bottom:`${e.$parent.$refs.roomFooter.clientHeight}px`}},[t("div",{staticClass:"vac-files-box"},e._l(e.files,(function(s,o){return t("div",{key:o},[t("room-file",{attrs:{file:s,index:o},on:{"remove-file":function(t){return e.$emit("remove-file",t)}}})],1)})),0),t("div",{staticClass:"vac-icon-close"},[t("div",{staticClass:"vac-svg-button",on:{click:function(t){return e.$emit("reset-message")}}},[e._t("reply-close-icon",(function(){return[t("svg-icon",{attrs:{name:"close-outline"}})]}))],2)])]):e._e()])},yt=[],At=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-room-file-container"},[t("div",{staticClass:"vac-svg-button vac-icon-remove",on:{click:function(t){return e.$emit("remove-file",e.index)}}},[e._t("reply-close-icon",(function(){return[t("svg-icon",{attrs:{name:"close-outline"}})]}))],2),e.isPdf?t("div",{staticClass:"vac-file-container"},[t("div",{staticClass:"file-icon"},[e._t("file-icon",(function(){return[t("img",{attrs:{src:e.pdfIcon,alt:"PDF Icon"}})]}))],2),t("div",{staticClass:"vac-text-ellipsis"},[e._v(" "+e._s(e.file.name)+" ")])]):t("div",{staticClass:"vac-file-container"},[t("div",{staticClass:"file-icon"},[e._t("file-icon",(function(){return[t("img",{attrs:{src:e.docIcon,alt:"PDF Icon"}})]}))],2),t("div",{staticClass:"vac-text-ellipsis"},[e._v(" "+e._s(e.file.name)+" ")])])])},bt=[];const{isPdfFile:_t}=s(6951);var wt={name:"RoomFiles",components:{SvgIcon:N},props:{file:{type:Object,required:!0},index:{type:Number,required:!0}},emits:["remove-file"],data(){return{pdfIcon:Le,docIcon:Be}},computed:{isPdf(){return _t(this.file)}}},Ct=wt,kt=(0,u.A)(Ct,At,bt,!1,null,null,null),Mt=kt.exports,It={name:"RoomFiles",components:{SvgIcon:N,RoomFile:Mt},props:{files:{type:Array,required:!0}},emits:["remove-file","reset-message"]},xt=It,St=(0,u.A)(xt,vt,yt,!1,null,null,null),Tt=St.exports,Ft=function(){var e=this,t=e._self._c;return t("transition",{attrs:{name:"vac-slide-up"}},[e.messageReply?t("div",{staticClass:"vac-reply-container",style:{bottom:`${e.$parent.$refs.roomFooter.clientHeight}px`}},[t("div",{staticClass:"vac-reply-box"},[t("span",{staticClass:"left-border"}),t("div",{staticClass:"vac-reply-wrapper"},[t("div",{staticClass:"vac-reply-info"},[t("div",{staticClass:"vac-reply-content"},[e.messageReply.username?t("div",{staticClass:"vac-reply-username"},[e._v(" "+e._s(e.messageReply.username)+" ")]):e._e(),t("format-message",{attrs:{content:e.messageReply.content,users:e.room.users,"text-formatting":e.textFormatting,"link-options":e.linkOptions,reply:!0},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)]),e.isImage?t("img",{staticClass:"vac-image-reply",attrs:{src:e.firstFile.url}}):e.isVideo?t("video",{staticClass:"vac-image-reply",attrs:{controls:""}},[t("source",{attrs:{src:e.firstFile.url}})]):e.isOtherFile?t("div",{staticClass:"vac-image-reply vac-file-container"},[t("div",{staticClass:"reply-icon"},[e._t("file-icon",(function(){return[t("img",{attrs:{src:e.docIcon}})]}))],2),t("div",{staticClass:"vac-text-ellipsis"},[e._v(" "+e._s(e.firstFile.name)+" ")]),e.firstFile.extension?t("div",{staticClass:"vac-text-ellipsis vac-text-extension"},[e._v(" "+e._s(e.firstFile.extension)+" ")]):e._e()]):e._e()])]),t("div",{staticClass:"vac-icon-reply"},[t("div",{staticClass:"vac-svg-button",on:{click:function(t){return e.$emit("reset-message")}}},[e._t("reply-close-icon",(function(){return[t("svg-icon",{attrs:{name:"close-outline"}})]}))],2)])]):e._e()])},Rt=[],Et=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-format-message-wrapper",class:{"vac-text-ellipsis":e.singleLine}},[e.textFormatting?t("div",{class:{"vac-text-ellipsis":e.singleLine}},[e.textFormatting?e._l(e.filteredLinkifiedMessage,(function(s,o){return t("div",{key:o,staticClass:"vac-format-container"},["group"===s.type?t("span",{staticClass:"msg msg_box"},e._l(s.parts,(function(s,o){return t(s.url?"a":"span",{key:o,tag:"component",class:{"vac-text-ellipsis":e.singleLine,"vac-text-bold":s.bold,"vac-text-italic":e.deleted||s.italic,"vac-text-strike":s.strike,"vac-text-underline":s.underline,"vac-text-inline-code":!e.singleLine&&s.inline,"vac-text-multiline-code":!e.singleLine&&s.multiline,"vac-text-tag":!e.singleLine&&!e.reply&&s.tag},attrs:{href:s.href,target:s.href?e.linkOptions.target:null,rel:s.href?e.linkOptions.rel:null}},[t("text-highlight",{attrs:{queries:e.queries}},[e._v(e._s(s.value))])],1)})),1):t(s.url?"a":"span",{tag:"component",class:{"vac-text-ellipsis":e.singleLine,"vac-text-bold":s.bold,"vac-text-italic":e.deleted||s.italic,"vac-text-strike":s.strike,"vac-text-underline":s.underline,"vac-text-inline-code":!e.singleLine&&s.inline,"vac-text-multiline-code":!e.singleLine&&s.multiline,"vac-text-tag":!e.singleLine&&!e.reply&&s.tag},attrs:{href:s.href,target:s.href?e.linkOptions.target:null,rel:s.href?e.linkOptions.rel:null}},[t("span",{staticClass:"msg msg_box"},[t("text-highlight",{attrs:{queries:e.queries}},[e._v(e._s(s.value))])],1)])],1)})):e._e()],2):t("div",[e._v(" "+e._s(e.formattedContent)+" ")])])},Lt=[],Bt=s(709);const Ot=s(9763);var Dt=(e,t)=>{e=Nt(e);const s=Pt(e),o=Ht(s),a=[].concat.apply([],o);return t&&qt(a),a};const Nt=e=>{e=e.replace(/\*\*/g,"*");const t=[...e.matchAll(/\*[^*\n]+\*/g)].map((e=>e.index));let s="",o=!1;for(let a=0;a<e.length;a++){if("*"===e[a]&&!o){const s=t.some((t=>a===t||a===t+e.slice(t).indexOf("*",1)));if(!s)continue}s+=e[a]}return s},Ut={bold:"*",italic:"_",strike:"~",underline:"°"},jt={[Ut.bold]:{end:"\\"+[Ut.bold],allowed_chars:".",type:"bold"},[Ut.italic]:{end:[Ut.italic],allowed_chars:"[^_]",type:"italic"},[Ut.strike]:{end:[Ut.strike],allowed_chars:".",type:"strike"},[Ut.underline]:{end:[Ut.underline],allowed_chars:".",type:"underline"},"```":{end:"```",allowed_chars:"(.|\n)",type:"multiline-code"},"`":{end:"`",allowed_chars:".",type:"inline-code"},"<usertag>":{allowed_chars:".",end:"</usertag>",type:"tag"}};function Pt(e){let t=[],s=-1,o=null;const a=Ot.find(e);let i=!1;if(a.length>0&&(s=e.indexOf(a[0].value),i=!0),Object.keys(jt).forEach((t=>{const a=e.indexOf(t);a>=0&&(s<0||a<s)&&(s=a,o=t,i=!1)})),i&&-1!==o){const o=e.substr(0,s),i=e.substr(s,a[0].value.length),n=e.substr(s+a[0].value.length);return t.push(o),t.push(i),t=t.concat(Pt(n)),t}if(o){let a=e.substr(0,s);const i=o;let n=e.substr(s+i.length);if(e.replace(/\s/g,"").length===2*i.length)return[e];const r=n.match(new RegExp("^("+(jt[i].allowed_chars||".")+"*"+(jt[i].end?"?":"")+")"+(jt[i].end?"("+jt[i].end+")":""),"m"));if(r&&r[1]){a&&t.push(a);const e={start:i,content:Pt(r[1]),end:r[2],type:jt[i].type};t.push(e),n=n.substr(r[0].length)}else a+=i,t.push(a);return t=t.concat(Pt(n)),t}return e?[e]:[]}function Ht(e){const t=[];return e.forEach((e=>{"string"===typeof e?t.push({types:[],value:e}):jt[e.start]&&t.push(Vt(e))})),t}function Vt(e){const t=[];return e.content.forEach((s=>{"string"===typeof s?t.push({types:[e.type],value:s}):s.content.forEach((o=>{"string"===typeof o?t.push({types:[s.type].concat([e.type]),value:o}):t.push({types:[o.type].concat([s.type]).concat([e.type]),value:Vt(o)})}))})),t}function qt(e){const t=[];return e.forEach((e=>{const s=Ot.find(e.value);if(s.length){const o=e.value.replace(s[0].value,"");t.push({types:e.types,value:o}),e.types=["url"].concat(e.types),e.href=s[0].href,e.value=s[0].value}t.push(e)})),t}var Gt=s(137),Qt={name:"FormatMessage",components:{SvgIcon:N,TextHighlight:Bt.A},props:{content:{type:[String,Number],required:!1},deleted:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},linkify:{type:Boolean,default:!0},singleLine:{type:Boolean,default:!1},reply:{type:Boolean,default:!1},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0},msgSearchQuery:{type:String,default:()=>""},isGroup:{type:Boolean,default:!0}},emits:["open-user-tag"],computed:{queries(){return[this.msgSearchQuery]},linkifiedMessage(){if(!this.content)return null;let e=Dt(this.formatTags(this.content),this.linkify&&!this.linkOptions.disabled,this.linkOptions);return e.forEach((e=>{e.value=this.cleanText(e.value),e.url=this.checkType(e,"url"),e.bold=this.checkType(e,"bold"),e.italic=this.checkType(e,"italic"),e.strike=this.checkType(e,"strike"),e.underline=this.checkType(e,"underline"),e.inline=this.checkType(e,"inline-code"),e.multiline=this.checkType(e,"multiline-code"),e.tag=this.checkType(e,"tag"),e.image=this.checkImageType(e)})),this.groupInlineMessages(e)},filteredLinkifiedMessage(){return Array.isArray(this.linkifiedMessage)?this.linkifiedMessage.filter((e=>"group"===e.type&&e.parts?.length||e.value&&e.value.trim())):[]}},methods:{groupInlineMessages(e){const t=[];let s=[];const o=()=>{s.length>0&&(t.push({type:"group",parts:s.map((e=>({...e})))}),s=[])};for(const a of e)"\n"===a.value||"\n\n"===a.value||/^\s*$/.test(a.value)?(o(),t.push(a)):s.push(a);return o(),t},cleanText(e){return e.replace(/[\u200B-\u200D\uFEFF\u00A0]/g,"")},checkType(e,t){return-1!==e.types.indexOf(t)},checkImageType(e){let t=e.value.lastIndexOf(".");const s=e.value.lastIndexOf("/");s>t&&(t=-1);const o=e.value.substring(t+1,e.value.length),a=t>0&&Gt.eG.some((e=>o.toLowerCase().includes(e)));return a&&this.setImageSize(e),a},setImageSize(e){const t=new Image;function s(o){if(!o?.path)return void t.removeEventListener("load",s);const a=o?.path[0].width/150;e.height=Math.round(o?.path[0]?.height/a)+"px",t.removeEventListener("load",s)}t.src=e.value,t.addEventListener("load",s)},formatTags(e){if(!e)return e;const t="<usertag>",s="</usertag>",o=[...e.matchAll(new RegExp(t,"gi"))].map((e=>e.index)),a=e;return o.forEach((o=>{const i=a.substring(o+t.length,a.indexOf(s,o)),n=this.users.find((e=>e._id===i));e=e.replaceAll(i,`@${n?.username||"unknown"}`)})),e},openTag(e){if(!this.singleLine&&this.checkType(e,"tag")){const t=this.users.find((t=>-1!==e.value.indexOf(t.username)));this.$emit("open-user-tag",t)}}}},zt=Qt,Yt=(0,u.A)(zt,Et,Lt,!1,null,null,null),Kt=Yt.exports;const{isImageFile:Jt,isVideoFile:Xt}=s(6951);var Zt={name:"RoomMessageReply",components:{SvgIcon:N,FormatMessage:Kt},props:{room:{type:Object,required:!0},messageReply:{type:Object,default:null},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0}},emits:["reset-message"],data(){return{docIcon:Be}},computed:{firstFile(){return this.messageReply.files?this.messageReply.files[0]:{}},isImage(){return Jt(this.firstFile)},isVideo(){return Xt(this.firstFile)},isOtherFile(){return this.messageReply.files&&!this.isVideo&&!this.isImage}}},Wt=Zt,$t=(0,u.A)(Wt,Ft,Rt,!1,null,null,null),es=$t.exports,ts=function(){var e=this,t=e._self._c;return t("transition",{attrs:{name:"vac-slide-up"}},[e.filteredEmojis.length?t("div",{staticClass:"vac-emojis-container",style:{bottom:`${e.$parent.$refs.roomFooter.clientHeight}px`}},e._l(e.filteredEmojis,(function(s,o){return t("div",{key:s,staticClass:"vac-emoji-element",class:{"vac-emoji-element-active":o===e.activeItem},on:{mouseover:function(t){e.activeItem=o},click:function(t){return e.$emit("select-emoji",s)}}},[e._v(" "+e._s(s)+" ")])})),0):e._e()])},ss=[],os={name:"RoomEmojis",props:{filteredEmojis:{type:Array,required:!0},selectItem:{type:Boolean,default:null},activeUpOrDown:{type:Number,default:null}},emits:["select-emoji","active-item"],data(){return{activeItem:null}},watch:{filteredEmojis(){this.activeItem=0},selectItem(e){e&&this.$emit("select-emoji",this.filteredEmojis[this.activeItem])},activeUpOrDown(){this.activeUpOrDown>0&&this.activeItem<this.filteredEmojis.length-1?this.activeItem++:this.activeUpOrDown<0&&this.activeItem>0&&this.activeItem--,this.$emit("activate-item")}}},as=os,is=(0,u.A)(as,ts,ss,!1,null,null,null),ns=is.exports,rs=function(){var e=this,t=e._self._c;return t("div",{ref:e.message._id,staticClass:"vac-message-wrapper",attrs:{id:e.message._id}},[e.showDate?t("div",{staticClass:"vac-card-info vac-card-date"},[e._v(" "+e._s(e.message.date)+" ")]):e._e(),e.unreadCounts[e.roomId]?.msg_id===e.message._id?t("div",{staticClass:"vac-line-new"},[e._v(" "+e._s(e.textMessages.NEW_MESSAGES)+" ")]):e._e(),e.message.system?t("div",{staticClass:"vac-card-info vac-card-system"},[t("format-message",{attrs:{content:e.message.content,users:e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"msg-search-query":e.msgSearchQuery}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2)],1):t("div",{staticClass:"vac-message-box",class:{"vac-offset-current":1===e.message.fromMe}},[e._t("message",(function(){return[t("div",{staticClass:"vac-message-container",class:{"vac-message-container-offset":e.messageOffset}},[t("div",{staticClass:"vac-message-card",class:{"vac-message-highlight":e.isMessageHover,"vac-message-wait":!e.message.saved&&1===e.message.fromMe,"vac-message-current":1===e.message.fromMe,"vac-message-deleted":e.message.deleted,"no-event":e.message.files?e.message.files[0].loading:null},attrs:{id:`${e.message._id}-child`},on:{mouseover:e.onHoverMessage,mouseleave:e.onLeaveMessage}},[e.isGroup&&e.messageOffset?t("div",{staticClass:"vac-text-username",class:{"vac-username-reply":!e.message.deleted&&e.message.replyMessage}},[t("span",[e._v(e._s(e.message.username))])]):e._e(),!e.message.deleted&&e.message.replyMessage?t("message-reply",{attrs:{message:e.message,"room-users":e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"is-group":e.isGroup},on:{"reply-msg-handler":function(t){return e.$emit("reply-msg-handler",e.message)}}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2):e._e(),e.message.deleted?t("div",[e._t("deleted-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-deleted",attrs:{name:"deleted"}})]})),t("span",[e._v(e._s(e.textMessages.MESSAGE_DELETED))])],2):e.message.files&&e.message.files.length?!e.isAudio||e.message.files.length>1?t("message-files",{attrs:{"current-user-id":e.currentUserId,message:e.message,"room-users":e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"msg-search-query":e.msgSearchQuery},on:{"open-file":e.openFile,"carousel-handler":e.carouselHandler}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2):[t("div",{class:{"vac-loading":e.message.files[0].loading}},[t("audio-player",{attrs:{src:e.message.files[0].url},on:{"update-progress-time":function(t){e.progressTime=t},"hover-audio-progress":function(t){e.hoverAudioProgress=t}}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2),e.message.deleted?e._e():t("div",{staticClass:"vac-progress-time"},[e._v(" "+e._s(e.progressTime)+" ")]),t("div",{staticClass:"vac-text-timestamp"},[e.message.edited&&!e.message.deleted?t("div",{staticClass:"vac-icon-edited"},[e._t("pencil-icon",(function(){return[t("svg-icon",{attrs:{name:"pencil"}})]}))],2):e._e(),t("span",[e._v(e._s(e.message.timestamp))]),e.isCheckmarkVisible?t("span",[e._t("checkmark-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-check",attrs:{name:"wait"!==e.message.distributed||e.message.saved?e.message.distributed?"double-checkmark":"checkmark":"wait",param:e.message.seen?"seen":""}})]}),null,{message:e.message})],2):e._e()])],1)]:t("format-message",{attrs:{content:e.message.content,users:e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"msg-search-query":e.msgSearchQuery}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2),e.showTimeStamp?t("div",{staticClass:"vac-text-timestamp"},[e.message.edited&&!e.message.deleted?t("div",{staticClass:"vac-icon-edited"},[e._t("pencil-icon",(function(){return[t("svg-icon",{attrs:{name:"pencil"}})]}))],2):e._e(),t("span",[e._v(e._s(e.message.timestamp))]),e.isCheckmarkVisible?t("span",{directives:[{name:"tooltip",rawName:"v-tooltip",value:e.message.reason,expression:"message.reason"}]},[e._t("checkmark-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-check",attrs:{reason:e.message.reason,name:e.message.failed?"error":"wait"!==e.message.distributed||e.message.saved?e.message.distributed?"double-checkmark":"checkmark":"wait",param:e.message.seen?"seen":""}})]}),null,{message:e.message})],2):e._e()]):e._e(),t("message-actions",{attrs:{"current-user-id":e.currentUserId,message:e.message,"message-actions":e.messageActions,"room-footer-ref":e.roomFooterRef,"show-reaction-emojis":e.showReactionEmojis,"hide-options":e.hideOptions,"message-hover":e.messageHover,"hover-message-id":e.hoverMessageId,"hover-audio-progress":e.hoverAudioProgress,"toggle-labels-modal":e.toggleLabelsModal},on:{"hide-options":function(t){return e.$emit("hide-options",!1)},"update-message-hover":function(t){e.messageHover=t},"update-options-opened":function(t){e.optionsOpened=t},"update-emoji-opened":function(t){e.emojiOpened=t},"message-action-handler":e.messageActionHandler,"send-message-reaction":e.sendMessageReaction,"open-forward-modal":e.openForwardModal,"toggle-error-modal":e.toggleErrorModal}},[e._l(e.$scopedSlots,(function(t,s){return[e._t(s,null,null,e.data)]}))],2)],2),t("message-reactions",{attrs:{"current-user-id":e.currentUserId,message:e.message},on:{"send-message-reaction":e.sendMessageReaction}})],1)]}),null,{message:e.message})],2)])},ls=[],cs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-reply-message",on:{click:function(t){return e.$emit("reply-msg-handler",e.message)}}},[e.isGroup?t("div",{staticClass:"vac-reply-username"},[e._v(" "+e._s(e.replyUsername)+" ")]):e._e(),e.isImage?t("div",{staticClass:"vac-image-reply-container"},[t("div",{staticClass:"vac-message-image vac-message-image-reply",style:{"background-image":`url('${e.firstFile.url}')`}})]):e.isVideo?t("div",{staticClass:"vac-video-reply-container"},[t("video",{attrs:{width:"100%",height:"100%",controls:""}},[t("source",{attrs:{src:e.firstFile.url}})])]):e.containsFile?t("div",{staticClass:"vac-reply-username hwa-filename"},[t("img",{staticClass:"d-block",attrs:{src:e.fileIcon,alt:"File Icon",height:"30"}}),t("span",{staticClass:"name"},[e._v(e._s(`${e.firstFile.name}.`))]),t("span",{staticClass:"ext"},[e._v(e._s(`${e.firstFile.extension}`))])]):e._e(),t("div",{staticClass:"vac-reply-content"},[t("format-message",{attrs:{content:e.message.replyMessage.content,users:e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,reply:!0},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)])},ds=[],us="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAAAsTAAALEwEAmpwYAAADwUlEQVR4nO3bT0zTUBwHcE38e/FiPHnRxIsnDxjjxSxKN7pX2J92ZQMEFAfIAmpQQRQXIBgQEBCHM5qoMajEhCgC4t+AIAhiFP+iEgVFjSYGQzxoVPKzDxXTUiYgrKt9Tb63dnu/T/te29d22jQfC8/zsyiGRXrGxvsrFOLC9QyXFcxwcb7a5pdFj7g6oTGgVCgjV69Y8UFBCTMpxA4qCTCEwLAeoTnT/Q5A0/RspYsfRkBshU6nm6EowL68FHjSUjDlQSZ+FASuEh+VigEc926HwdfeKU+oWR7gV3eo0eli52gGwOSIAUOYTQyBuIsreX6uJgA2pO2A0poqoM126ZjQRNNR8zQBcK73IZTVnQWjxS7pEmxHsMUyXxMAOAev1gFiI6UId2iaX6AJABzvtQvA2CIl3YHrWsPwCzUBgHOk+TKY7NHSMeGFPoxfrAkAnKM3G8ASuV5ydmBfGsL4JaoHYCzhfwC2pcsC4BxrbwRr1DrpKfINhSxLVQ3AhjuG/9Pu3DgqAM6J283AxjglF0vcewNjXaZaAFeS+ND2XDrvE6GiswX49YnSK8Z+A8OvUCVA7elMUTHWqDg40nTFJ8LJ+21gj3dJEQZCGG6V6gBw+IgIUTEhwg1SktsNuw56wH34kGzSSotHXDYLZ4evlNHqVB1Az61CoEe5K5xIKCO3XFUAOE9a94KZDZ8cAIbbqToAnG+vvOAp2gI2hwMMof8AgDhWlQDS9Hfth7edJX9NU022CCAYceb/AmCseXA9nwAQAAJAAAgAAfAXQN/dYmiszvZr7jfmBQZAd1sBIB8PM6YyVScylAeortipSPE4mRkblQd496AEIqKk09VTH3zU3ajNUR4A50tvOXS3F/rl4envDDw7EBhjQCCGABAAAkAA/ApQcyoTcrNckONOkk1x3iZ40VEk39imfCjckyxa31uSCv1dZeoAaKnLGdN5Ozp27YhtP/eUg8Umfe7/M9m7XeoAqD/jHhMALvR7n3jbge4DQJtssutvTXWqAwBfBO3NTYbYddEQI+xluSQmxA5NVMptX1uZCc74GNH6m1Pi4GlrgToAAjEEgAAQAAJAAAgAASAABIAAEAACoEmAew15IgAKWRlNAVyoFL9saWD4IM0A4FkmPL8w3HbEfprQh1ZqBPjwuBR27ZC8N4zY4+MuXg4Aq4426RkIyUhPAJP0xUph74eEmBdNCoAK880QylomVPyvZboecR8DoJDxB389gtjV/1L80KJnrDahDz0UfvR54Id9JOQcZbQ5xzPo/QDIVBjGD/X8XgAAAABJRU5ErkJggg==";const{isImageFile:ms,isVideoFile:gs}=s(6951);var ps={name:"MessageReply",components:{FormatMessage:Kt},props:{message:{type:Object,required:!0},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0},roomUsers:{type:Array,required:!0},isGroup:{type:Boolean,default:!1}},emits:["reply-msg-handler"],data(){return{fileIcon:us}},computed:{replyUsername(){const{username:e}=this.message.replyMessage;return e||""},firstFile(){return this.message.replyMessage.files?this.message.replyMessage.files[0]:{}},containsFile(){return this.message.replyMessage.files},isImage(){return ms(this.firstFile)},isVideo(){return gs(this.firstFile)}}},hs=ps,fs=(0,u.A)(hs,cs,ds,!1,null,null,null),vs=fs.exports,ys=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-message-files-container",class:{"with-reply":e.message.replyMessage}},[t("div",{staticClass:"clearfix"},[1===e.imageFiles.length?t("div",{staticClass:"hwa-single-image"},[t("message-file",{attrs:{file:e.imageFiles[0],"current-user-id":e.currentUserId,message:e.message,index:0},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1):e.imageFiles.length<=3?t("div",e._l(e.imageFiles,(function(s,o){return t("div",{key:o+"iv",staticClass:"hwa-single-image"},[t("message-file",{attrs:{file:s,"current-user-id":e.currentUserId,message:e.message,index:o},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)})),0):4===e.imageFiles.length?t("div",e._l(e.imageFiles,(function(s,o){return t("div",{key:o+"iv",staticClass:"hwa-multiple-images"},[t("message-file",{attrs:{file:s,"current-user-id":e.currentUserId,message:e.message,index:o},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)})),0):t("div",[e._l(e.imageFiles.slice(0,3),(function(s,o){return t("div",{key:o+"iv",staticClass:"hwa-multiple-images"},[t("message-file",{attrs:{file:s,"current-user-id":e.currentUserId,message:e.message,index:o},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],1)})),t("div",{staticClass:"hwa-multiple-images more-images",class:{"more-images-loading":e.imageFiles[3].loading},on:{click:function(t){return t.preventDefault(),e.handleCarousel(e.imageFiles,e.imageFiles[3])}}},[t("message-file",{attrs:{file:e.imageFiles[3],"current-user-id":e.currentUserId,message:e.message,index:3},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}),t("span",{staticClass:"images-num"},[e._v(" +"+e._s(e.imageFiles.length-4)+" ")])],1)],2)]),e.videoFiles?t("div",e._l(e.videoFiles,(function(s,o){return t("div",{key:o+"iv",staticClass:"hwa-video-container"},[t("message-file",{attrs:{file:s,"current-user-id":e.currentUserId,message:e.message,index:o},on:{"open-file":function(t){return e.$emit("open-file",t)}},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)}),t("div",{staticClass:"video-timestamp"},[t("span",[e._v(e._s(e.message.timestamp))]),e.isCheckmarkVisible?t("span",[e._t("checkmark-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-check",attrs:{name:e.message.distributed?"double-checkmark":"checkmark",param:e.message.seen?"seen":""}})]}),null,{message:e.message})],2):e._e()])],1)})),0):e._e(),e._l(e.otherFiles,(function(s,o){return t("div",{key:o+"a",staticClass:"position-relative"},[t("upload-state",{attrs:{show:s.loading||s.error,loading:!1,error:s.error}}),t("div",{staticClass:"vac-file-container",on:{click:function(t){return t.stopPropagation(),e.openFile(s,"download")}}},[t("div",{staticClass:"hwa-file-display"},["pdf"===s.extension?t("embed",{class:{"vac-blur":s.loading||s.error},attrs:{src:`${s.loading?s.localUrl:s.url}`,type:"application/pdf"}}):t("img",{staticClass:"file-icon",class:{"vac-blur":s.loading||s.error},attrs:{src:"pdf"===s.extension?e.pdfIcon:e.docIcon,alt:"Pdf Icon"}})]),t("div",{staticClass:"hwa-file-info"},[t("img",{staticClass:"file-icon",attrs:{src:"pdf"===s.extension?e.pdfIcon:e.docIcon,alt:"Pdf Icon"}}),t("div",{staticClass:"vac-text-ellipsis"},[e._v(" "+e._s(decodeURIComponent(s.extension?`${s.name}.${s.extension}`:s.name))+" ")]),t("img",{staticClass:"download-icon",attrs:{src:1===e.message.fromMe?e.downloadIcon:e.downloadIconDark,alt:"Download Icon"}})]),t("div",{staticClass:"hwa-file-meta"},[t("div",{staticClass:"file-info"}),t("div",{staticClass:"mr-4"},[t("span",[e._v(e._s(e.message.timestamp))]),e.isCheckmarkVisible?t("span",[e._t("checkmark-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-check",attrs:{name:e.message.distributed?"double-checkmark":"checkmark",param:e.message.seen?"seen":""}})]}),null,{message:e.message})],2):e._e()])])])],1)})),t("format-message",{attrs:{content:e.message.content,users:e.roomUsers,"text-formatting":e.textFormatting,"link-options":e.linkOptions,"msg-search-query":e.msgSearchQuery},scopedSlots:e._u([e._l(e.$scopedSlots,(function(t,s){return{key:s,fn:function(t){return[e._t(s,null,null,t)]}}}))],null,!0)})],2)},As=[],bs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAkCAYAAAAD3IPhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAP1SURBVHgBxZhHaBVBHMZnbdHEYGJB1KBgA2NFsXsQxSCC4sUCAStYUEEsiA30YDl4sSsaD5aDYEFBCYooiHoRK0YPYkNijSUaSxIzfp/zLXmGvPd2kmf8w4/J7vuXLzuzUzYwdTBrbTs0uaA7yAYtwHdQAp6AoiAI3hlPC6I4oTj9uoExYCLoD9JBBmgKGoEqUCE+gzvgLLgAXkCcTVYnqRgI6YJmKZgCOqhwJXgNPoAyUC5RFMin1hakSWAxKAAHIeilqYtBRFMwGdwBVaIYHAB5oDvIkl8AmoG2uj8O7AGvYmLvg3z6+wrJALtAKSgHRWAx6OSZpxNYCZ5ZZ5/BZuaPmiAT7FMw/6NToLOph7GrwWnlo+0FLZMFtQDHFFAG5vHxmxSYunE++KH8R1gvnnMTsFrO7JqZoLFJoanGEuVnnUWgUW2Ow8Brqd7JQPMPTAN+l548B/jQmg4twSUJYZvpkTxX46q/RwzHZaHqnfprQONiOvgGvoKRxsNiBvs+z7hRejpkKu+F/ZVv3JR+EdwwftZcbZrxs+vgknETZf4fMVDVB+1w42bS7Zglq0wDmOpsM272HsHu5pPJA1ngISgyDWu3wGPjFts8ihkM+Ao/AN4rbT2NvXFb9YdQTC/9cDXKyppKU717uuxFMTm6eGT+jz1Tm0Mx4ZxSEs8bgysNbAXnoi6WWiTpv4nxCVw/qc2kmHBPk+gt4vo0DkwAnKx6msRC+Huh/McrPpkFFFOqi+y4XkHwBc0y8BRwKjiEgr3jCOGkWSA/+s9RfDxrpbaUYl7poodJbJykZqsAC+6vKUjXW8Eo8BbMhZC7SfKG25NiJjip6XyHiWDW7eLCBfUquKy/r+iae5Y39IuQizvEAsWf4A3uxCrBLZtsw1O7oJr2BORFzJMNbqr+Ct4YAN77LpLwHV2LIAoZ7ZEjXCzfgX7hzXA5P27dsSRqssExgtg1Az1iuS6Gu8rC2B9mWLeFKAEjjIfBvy9YA3I94wbpqbBHpsX+0AZc0+Djxjny5qouZt3m6ryeCuul13QYq6dDtWttbXvT1Ahh96xXHXbxhHhOq6SWG+bJPuMnohC+yjOVn72wzsbba1t3VDkqQVS+wKb2qLLQVh9VDoPmyYLa2+qJ8KN1J4XWph6G+BywW/loJ1gnanCGXrtSBT+ybtPuJYr+YLbiK8Av687g0Y63MYnSrTtk3VP/stv4EWCLdTNwDxVrprHA81CW7o+R310JqFKeWTbBwT/KJ5GuaJaDSaCjcSeKn+C54GcRbqo5tlrLhx+Rwk8iXIjPGPdJ5Lapr+k/7wM26j+ssImtQn4b9KQivZV1enWRnB+Dkn1Ge2887TdiRJ7S/bFxbAAAAABJRU5ErkJggg==",_s="data:image/png;base64,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",ws=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-message-file-container"},[e.isImage?t("div",{ref:"imageRef"+e.index,staticClass:"vac-message-image-container",on:{mouseover:function(t){e.imageHover=!0},mouseleave:function(t){e.imageHover=!1},click:function(t){return t.stopPropagation(),e.openFile("preview")}}},[t("upload-state",{attrs:{show:e.inUploadState,loading:!1,error:e.file.error}}),t("div",{staticClass:"vac-message-image",class:{"vac-loading":e.inUploadState&&1===e.message.fromMe,"mb-8":e.message.content},style:{"background-image":`url('${e.inUploadState&&e.file.localUrl||e.file.url}')`,"max-height":`${e.imageResponsive.maxHeight}px`}},[t("transition",{attrs:{name:"vac-fade-image"}},[e.inUploadState?e._e():t("div",{staticClass:"vac-image-buttons"},[t("div",{staticClass:"hwa-text-timestamp"},[t("span",{staticClass:"d-inline-block"},[e._v(e._s(e.file.timestamp))]),e.isCheckmarkVisible?t("span",[e._t("checkmark-icon",(function(){return[t("svg-icon",{staticClass:"vac-icon-check",attrs:{name:e.file.distributed?"double-checkmark":"checkmark",param:e.file.seen?"seen":""}})]}),null,{file:e.file})],2):e._e()])])])],1)],1):e.isVideo?t("div",{staticClass:"vac-video-container",class:{"mb-2":e.isAudio}},[t("upload-state",{attrs:{show:e.inUploadState,loading:!1,error:e.file.error}}),t("div",{class:{"vac-loading":e.inUploadState&&1===e.message.fromMe}},[t("video",{attrs:{width:"100%",height:"100%",controls:""}},[t("source",{attrs:{src:e.inUploadState?e.file.localUrl:e.file.url}})])])],1):e._e()])},Cs=[],ks=function(){var e=this,t=e._self._c;return e.show?t("div",{staticClass:"upload-state"},[e.loading?t("div",{staticClass:"upload-state-inner ball-pulse"},[t("div"),t("div"),t("div")]):e._e(),e.error?t("div",[t("img",{attrs:{src:e.errorIcon,alt:"Error Icon"}})]):e._e()]):e._e()},Ms=[],Is={name:"UploadState",props:{show:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},error:{type:Boolean,default:!0}},data(){return{errorIcon:G}}},xs=Is,Ss=(0,u.A)(xs,ks,Ms,!1,null,null,null),Ts=Ss.exports;const{isImageFile:Fs,isVideoFile:Rs,isAudioFile:Es}=s(6951);var Ls={name:"MessageFile",components:{SvgIcon:N,UploadState:Ts},props:{message:{type:Object,required:!0},file:{type:Object,required:!0},index:{type:Number,required:!0}},emits:["open-file"],data(){return{imageResponsive:"",imageLoading:!1,imageHover:!1}},computed:{isAudio(){return Es(this.file)},inUploadState(){return this.file.loading||this.file.error},isImage(){return Fs(this.file)},isVideo(){return Rs(this.file)},isCheckmarkVisible(){return 1===this.message.fromMe&&!this.message.deleted&&(this.message.saved||this.message.distributed||this.message.seen)}},watch:{file:{immediate:!0,handler(){this.checkImgLoad()}}},mounted(){const e=this.$refs["imageRef"+this.index];e&&(this.imageResponsive={maxHeight:e.clientWidth-18,loaderTop:e.clientHeight/2-9})},methods:{checkImgLoad(){if(!Fs(this.file))return;this.imageLoading=!0;const e=new Image;e.src=this.file.url,e.addEventListener("load",(()=>this.imageLoading=!1))},openFile(e){this.inUploadState||this.$emit("open-file",{file:this.file,action:e})}}},Bs=Ls,Os=(0,u.A)(Bs,ws,Cs,!1,null,null,null),Ds=Os.exports;const{isImageVideoFile:Ns,isVideoFile:Us,isImageFile:js}=s(6951);var Ps={name:"MessageFiles",components:{SvgIcon:N,FormatMessage:Kt,MessageFile:Ds,UploadState:Ts},props:{currentUserId:{type:[String,Number],required:!0},message:{type:Object,required:!0},roomUsers:{type:Array,required:!0},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0},msgSearchQuery:{type:String,required:!0}},emits:["open-file","carousel-handler"],data(){return{pdfIcon:Le,docIcon:Be,downloadIcon:bs,downloadIconDark:_s}},computed:{imageFiles(){return this.message.files.filter((e=>js(e)))},videoFiles(){return this.message.files.filter((e=>Us(e)))},otherFiles(){return this.message.files.filter((e=>!Ns(e)))},isCheckmarkVisible(){return 1===this.message.fromMe&&!this.message.deleted&&(this.message.saved||this.message.distributed||this.message.seen)}},methods:{openFile(e,t){e.loading||e.error||this.$emit("open-file",{file:e,action:t})},handleCarousel(e,t){t.loading||t.error||this.$emit("carousel-handler",e)}}},Hs=Ps,Vs=(0,u.A)(Hs,ys,As,!1,null,null,null),qs=Vs.exports,Gs=function(){var e=this,t=e._self._c;return t("div",{staticClass:"vac-message-actions-wrapper"},[e.message.error?e._e():t("div",{staticClass:"vac-options-container",class:{"files-variant":e.message.files},style:{display:e.hoverAudioProgress?"none":"initial",width:(e.filteredMessageActions.length&&e.showReactionEmojis,"45px")}},[t("transition-group",{attrs:{name:"vac-slide-left",tag:"span"}},[e.isMessageActions||e.isMessageReactions?t("div",{key:"1",staticClass:"vac-blur-container",class:{"vac-options-me":1===e.message.fromMe}}):e._e(),e.isMessageActions?t("div",{key:"2",ref:"actionIcon",staticClass:"vac-svg-button vac-message-options",on:{click:e.openOptions}},[e._t("dropdown-icon",(function(){return[t("svg-icon",{attrs:{name:"dropdown",param:"message"}})]}))],2):e._e()])],1),e.filteredMessageActions.length?t("transition",{attrs:{name:1===e.message.fromMe?"vac-slide-left":"vac-slide-right"}},[e.optionsOpened?t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.closeOptions,expression:"closeOptions"}],ref:"menuOptions",staticClass:"vac-menu-options",class:{"vac-menu-left":!e.message.fromMe},style:{top:`${e.menuOptionsTop}px`}},[t("div",{staticClass:"vac-menu-list"},e._l(e.filteredMessageActions,(function(s){return t("div",{key:s.name},[t("div",{staticClass:"vac-menu-item",on:{click:function(t){return e.messageActionHandler(s)}}},[e._v(" "+e._s(s.title)+" ")])])})),0)]):e._e()]):e._e(),e.message.error?t("div",{staticClass:"message-error-icon",on:{click:function(t){return t.preventDefault(),e.$emit("toggle-error-modal")}}},[t("img",{attrs:{src:e.errorIcon,alt:"Error Icon"}})]):e._e()],1)},Qs=[],zs={name:"MessageActions",components:{SvgIcon:N},directives:{clickOutside:pe().directive},props:{message:{type:Object,required:!0},messageActions:{type:Array,required:!0},roomFooterRef:{type:HTMLDivElement,default:null},showReactionEmojis:{type:Boolean,required:!0},hideOptions:{type:Boolean,required:!0},messageHover:{type:Boolean,required:!0},hoverMessageId:{type:[String,Number],default:null},hoverAudioProgress:{type:Boolean,required:!0}},emits:["update-emoji-opened","update-options-opened","update-message-hover","hide-options","message-action-handler","send-message-reaction","open-forward-modal","toggle-message-forward"],data(){return{menuOptionsTop:0,optionsOpened:!1,optionsClosing:!1,emojiOpened:!1,errorIcon:G}},computed:{isMessageActions(){return this.filteredMessageActions.length&&this.messageHover&&!this.message.deleted&&!this.message.disableActions&&!this.hoverAudioProgress},isMessageReactions(){return this.showReactionEmojis&&this.messageHover&&!this.message.deleted&&!this.message.disableReactions&&!this.hoverAudioProgress},filteredMessageActions(){return 1===this.message.fromMe?this.messageActions:this.messageActions.filter((e=>!e.onlyMe))}},watch:{emojiOpened(e){this.$emit("update-emoji-opened",e),e&&(this.optionsOpened=!1)},hideOptions(e){e&&(this.closeEmoji(),this.closeOptions())},optionsOpened(e){this.$emit("update-options-opened",e)}},methods:{openOptions(){this.optionsClosing||(this.optionsOpened=!this.optionsOpened,this.optionsOpened&&(this.$emit("hide-options",!1),setTimeout((()=>{if(!this.roomFooterRef||!this.$refs.menuOptions||!this.$refs.actionIcon)return;const e=this.$refs.menuOptions.getBoundingClientRect().height,t=this.$refs.actionIcon.getBoundingClientRect().top,s=this.roomFooterRef.getBoundingClientRect().top,o=s-t>e+50;this.menuOptionsTop=o?30:-e}))))},closeOptions(){this.optionsOpened=!1,this.optionsClosing=!0,this.updateMessageHover(),setTimeout((()=>this.optionsClosing=!1),100)},closeEmoji(){this.emojiOpened=!1,this.updateMessageHover()},updateMessageHover(){this.hoverMessageId!==this.message._id&&this.$emit("update-message-hover",!1)},messageActionHandler(e){this.closeOptions(),this.$emit("message-action-handler",e)}}},Ys=zs,Ks=(0,u.A)(Ys,Gs,Qs,!1,null,null,null),Js=Ks.exports,Xs=function(){var e=this,t=e._self._c;return e.message.deleted?e._e():t("transition-group",{attrs:{name:"vac-slide-left",tag:"span"}},e._l(e.message.reactions,(function(s,o){return t("button",{directives:[{name:"show",rawName:"v-show",value:s.length,expression:"reaction.length"}],key:o+0,staticClass:"vac-button-reaction",class:{"vac-reaction-me":-1!==s.indexOf(e.currentUserId)},style:{float:1===e.message.fromMe?"right":"left"},on:{click:function(t){return e.sendMessageReaction({unicode:o},s)}}},[e._v(" "+e._s(o)),t("span",[e._v(e._s(s.length))])])})),0)},Zs=[],Ws={name:"MessageReactions",props:{currentUserId:{type:[String,Number],required:!0},message:{type:Object,required:!0}},emits:["send-message-reaction"],methods:{sendMessageReaction(e,t){this.$emit("send-message-reaction",{emoji:e,reaction:t})}}},$s=Ws,eo=(0,u.A)($s,Xs,Zs,!1,null,null,null),to=eo.exports,so=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"vac-audio-player"},[t("div",{staticClass:"vac-svg-button",on:{click:e.playback}},[e.isPlaying?e._t("audio-pause-icon",(function(){return[t("svg-icon",{attrs:{name:"audio-pause"}})]})):e._t("audio-play-icon",(function(){return[t("svg-icon",{attrs:{name:"audio-play"}})]}))],2),t("audio-control",{attrs:{percentage:e.progress},on:{"change-linehead":e.onUpdateProgress,"hover-audio-progress":function(t){return e.$emit("hover-audio-progress",t)}}}),t("audio",{attrs:{id:e.playerUniqId,src:e.audioSource}})],1)])},oo=[],ao=function(){var e=this,t=e._self._c;return t("div",{ref:"progress",staticClass:"vac-player-bar",on:{mousedown:e.onMouseDown,mouseover:function(t){return e.$emit("hover-audio-progress",!0)},mouseout:function(t){return e.$emit("hover-audio-progress",!1)}}},[t("div",{staticClass:"vac-player-progress"},[t("div",{staticClass:"vac-line-container"},[t("div",{staticClass:"vac-line-progress",style:{width:`${e.percentage}%`}}),t("div",{staticClass:"vac-line-dot",class:{"vac-line-dot__active":e.isMouseDown},style:{left:`${e.percentage}%`}})])])])},io=[],no={props:{percentage:{type:Number,default:0}},emits:["hover-audio-progress","change-linehead"],data(){return{isMouseDown:!1}},methods:{onMouseDown(e){this.isMouseDown=!0;const t=this.calculateLineHeadPosition(e,this.$refs.progress);this.$emit("change-linehead",t),document.addEventListener("mousemove",this.onMouseMove),document.addEventListener("mouseup",this.onMouseUp)},onMouseUp(e){this.isMouseDown=!1,document.removeEventListener("mouseup",this.onMouseUp),document.removeEventListener("mousemove",this.onMouseMove);const t=this.calculateLineHeadPosition(e,this.$refs.progress);this.$emit("change-linehead",t)},onMouseMove(e){const t=this.calculateLineHeadPosition(e,this.$refs.progress);this.$emit("change-linehead",t)},calculateLineHeadPosition(e,t){const s=t.getBoundingClientRect().width,o=t.getBoundingClientRect().left;let a=(e.clientX-o)/s;return a=a<0?0:a,a=a>1?1:a,a}}},ro=no,lo=(0,u.A)(ro,ao,io,!1,null,null,null),co=lo.exports,uo={name:"AudioPlayer",components:{SvgIcon:N,AudioControl:co},props:{src:{type:String,default:null}},emits:["hover-audio-progress","update-progress-time"],data(){return{isPlaying:!1,duration:this.convertTimeMMSS(0),playedTime:this.convertTimeMMSS(0),progress:0}},computed:{playerUniqId(){return`audio-player${this._uid}`},audioSource(){return this.src?this.src:(this.resetProgress(),null)}},mounted(){this.player=document.getElementById(this.playerUniqId),this.player.addEventListener("ended",(()=>{this.isPlaying=!1})),this.player.addEventListener("loadeddata",(()=>{this.resetProgress(),this.duration=this.convertTimeMMSS(this.player.duration),this.updateProgressTime()})),this.player.addEventListener("timeupdate",this.onTimeUpdate)},methods:{convertTimeMMSS(e){return new Date(1e3*e).toISOString().substr(14,5)},playback(){this.audioSource&&(this.isPlaying?this.player.pause():setTimeout((()=>this.player.play())),this.isPlaying=!this.isPlaying)},resetProgress(){this.isPlaying&&this.player.pause(),this.duration=this.convertTimeMMSS(0),this.playedTime=this.convertTimeMMSS(0),this.progress=0,this.isPlaying=!1,this.updateProgressTime()},onTimeUpdate(){this.playedTime=this.convertTimeMMSS(this.player.currentTime),this.progress=this.player.currentTime/this.player.duration*100,this.updateProgressTime()},onUpdateProgress(e){e&&(this.player.currentTime=e*this.player.duration)},updateProgressTime(){this.$emit("update-progress-time",this.progress>1?this.playedTime:this.duration)}}},mo=uo,go=(0,u.A)(mo,so,oo,!1,null,null,null),po=go.exports;const{messagesValidation:ho}=s(8900),{isAudioFile:fo}=s(6951);var vo={name:"Message",components:{SvgIcon:N,FormatMessage:Kt,AudioPlayer:po,MessageReply:vs,MessageFiles:qs,MessageActions:Js,MessageReactions:to},props:{currentUserId:{type:[String,Number],required:!0},textMessages:{type:Object,required:!0},index:{type:Number,required:!0},message:{type:Object,required:!0},messages:{type:Array,required:!0},editedMessage:{type:Object,required:!0},roomUsers:{type:Array,default:()=>[]},messageActions:{type:Array,required:!0},roomFooterRef:{type:HTMLDivElement,default:null},newMessages:{type:Array,default:()=>[]},showReactionEmojis:{type:Boolean,required:!0},showNewMessagesDivider:{type:Boolean,required:!0},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0},hideOptions:{type:Boolean,required:!0},msgSearchQuery:{type:String,required:!0},toggleLabelsModal:{type:Function,default:()=>({})},isGroup:{type:Boolean,default:!1},unreadCounts:{type:Object,required:!0},roomId:{type:[String,Number],required:!0}},emits:["hide-options","message-added","open-file","message-action-handler","send-message-reaction","carousel-handler","open-forward-modal","reply-msg-handler"],data(){return{hoverMessageId:null,messageHover:!1,optionsOpened:!1,emojiOpened:!1,newMessage:{},progressTime:"- : -",hoverAudioProgress:!1}},computed:{showTimeStamp(){return!this.message.files||this.message.files&&this.message.deleted},showDate(){return this.index>0&&this.message.date!==this.messages[this.index-1].date},messageOffset(){return 0===this.index||this.index>0&&this.message.username!==this.messages[this.index-1].username},isMessageHover(){return this.editedMessage._id===this.message._id||this.hoverMessageId===this.message._id},isAudio(){return this.message.files?.some((e=>fo(e)))},isCheckmarkVisible(){return 1===this.message.fromMe&&!this.message.deleted&&(this.message.saved||this.message.distributed||this.message.seen||"wait"===this.message.distributed||this.message.failed)}},watch:{newMessages:{immediate:!0,deep:!0,handler(e){if(!e.length||!this.showNewMessagesDivider)return this.newMessage={};this.newMessage=e.reduce(((e,t)=>t.index<e.index?t:e))}}},mounted(){ho(this.message),this.$emit("message-added",{message:this.message,index:this.index,ref:this.$refs[this.message._id]})},methods:{onHoverMessage(){this.messageHover=!0,this.canEditMessage()&&(this.hoverMessageId=this.message._id)},canEditMessage(){return!this.message.deleted},onLeaveMessage(){this.optionsOpened||this.emojiOpened||(this.messageHover=!1),this.hoverMessageId=null},openFile(e){this.$emit("open-file",{message:this.message,file:e})},messageActionHandler(e){this.messageHover=!1,this.hoverMessageId=null,setTimeout((()=>{this.$emit("message-action-handler",{action:e,message:this.message})}),300)},sendMessageReaction({emoji:e,reaction:t}){this.$emit("send-message-reaction",{messageId:this.message._id,reaction:e,remove:t&&-1!==t.indexOf(this.currentUserId)}),this.messageHover=!1},carouselHandler(e){this.$emit("carousel-handler",e)},openForwardModal(e){this.$emit("open-forward-modal",e)},toggleErrorModal(){this.$emit("toggle-error-modal")}}},yo=vo,Ao=(0,u.A)(yo,rs,ls,!1,null,null,null),bo=Ao.exports,_o=(e,t,s,o,a=!1)=>o&&""!==o?e.filter((e=>a?wo(e[t]).startsWith(wo(o))||wo(e[s]).startsWith(wo(o)):wo(e[t]).includes(wo(o))||wo(e[s]).includes(wo(o)))):e;function wo(e){return e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"")}s(6573),s(8100),s(7936),s(7467),s(4732),s(9577);let Co;try{Co=s(5900)}catch(ga){Co={missing:!0}}const{Mp3Encoder:ko}=Co;var Mo=class{constructor(e){if(Co.missing)throw new Error('You must add lamejs in your dependencies to use the audio recorder. Please run "npm install lamejs --save"');this.bitRate=e.bitRate,this.sampleRate=e.sampleRate,this.dataBuffer=[],this.encoder=new ko(1,this.sampleRate,this.bitRate)}encode(e){const t=1152,s=this._convertBuffer(e);let o=s.length;for(let a=0;o>=0;a+=t){const e=s.subarray(a,a+t),i=this.encoder.encodeBuffer(e);this.dataBuffer.push(new Int8Array(i)),o-=t}}finish(){this.dataBuffer.push(this.encoder.flush());const e=new Blob(this.dataBuffer,{type:"audio/mp3"});return this.dataBuffer=[],{id:Date.now(),blob:e,url:URL.createObjectURL(e)}}_floatTo16BitPCM(e,t){for(let s=0;s<e.length;s++){const o=Math.max(-1,Math.min(1,e[s]));t[s]=o<0?32768*o:32767*o}}_convertBuffer(e){const t=new Float32Array(e),s=new Int16Array(e.length);return this._floatTo16BitPCM(t,s),s}},Io=class{constructor(e={}){this.beforeRecording=e.beforeRecording,this.pauseRecording=e.pauseRecording,this.afterRecording=e.afterRecording,this.micFailed=e.micFailed,this.encoderOptions={bitRate:e.bitRate,sampleRate:e.sampleRate},this.bufferSize=4096,this.records=[],this.isPause=!1,this.isRecording=!1,this.duration=0,this.volume=0,this._duration=0}start(){const e={video:!1,audio:{channelCount:1,echoCancellation:!1}};this.beforeRecording&&this.beforeRecording("start recording"),navigator.mediaDevices.getUserMedia(e).then(this._micCaptured.bind(this)).catch(this._micError.bind(this)),this.isPause=!1,this.isRecording=!0,this.lameEncoder||(this.lameEncoder=new Mo(this.encoderOptions))}stop(){this.stream.getTracks().forEach((e=>e.stop())),this.input.disconnect(),this.processor.disconnect(),this.context.close();let e=null;e=this.lameEncoder.finish(),e.duration=this.duration,this.records.push(e),this._duration=0,this.duration=0,this.isPause=!1,this.isRecording=!1,this.afterRecording&&this.afterRecording(e)}pause(){this.stream.getTracks().forEach((e=>e.stop())),this.input.disconnect(),this.processor.disconnect(),this._duration=this.duration,this.isPause=!0,this.pauseRecording&&this.pauseRecording("pause recording")}_micCaptured(e){this.context=new(window.AudioContext||window.webkitAudioContext),this.duration=this._duration,this.input=this.context.createMediaStreamSource(e),this.processor=this.context.createScriptProcessor(this.bufferSize,1,1),this.stream=e,this.processor.onaudioprocess=e=>{const t=e.inputBuffer.getChannelData(0);let s=0;this.lameEncoder&&this.lameEncoder.encode(t);for(let o=0;o<t.length;++o)s+=t[o]*t[o];this.duration=parseFloat(this._duration)+parseFloat(this.context.currentTime.toFixed(2)),this.volume=Math.sqrt(s/t.length).toFixed(2)},this.input.connect(this.processor),this.processor.connect(this.context.destination)}_micError(e){this.micFailed&&this.micFailed(e)}};const{detectMobile:xo,iOSDevice:So}=s(902),To=(e,t)=>{let s;return function(){const o=this,a=arguments;clearTimeout(s),s=setTimeout((()=>e.apply(o,a)),t)}};var Fo={name:"Room",components:{InfiniteLoading:me(),Loader:_e,SvgIcon:N,EmojiPickerContainer:Te,RoomHeader:ft,RoomFiles:Tt,RoomMessageReply:es,RoomEmojis:ns,Message:bo,UploadModal:je,Info:ze},directives:{clickOutside:pe().directive},props:{currentUserId:{type:[String,Number],required:!0},textMessages:{type:Object,required:!0},singleRoom:{type:Boolean,required:!0},showRoomsList:{type:Boolean,required:!0},isMobile:{type:Boolean,required:!0},rooms:{type:Array,required:!0},roomId:{type:[String,Number],required:!0},loadFirstRoom:{type:Boolean,required:!0},messages:{type:Array,required:!0},roomMessage:{type:String,default:null},messagesLoaded:{type:Boolean,required:!0},menuActions:{type:Array,required:!0},messageActions:{type:Array,required:!0},showSendIcon:{type:Boolean,required:!0},isMsgFetched:{type:Boolean,default:!0},showFiles:{type:Boolean,required:!0},showAudio:{type:Boolean,required:!0},audioBitRate:{type:Number,required:!0},audioSampleRate:{type:Number,required:!0},showEmojis:{type:Boolean,required:!0},showReactionEmojis:{type:Boolean,required:!0},showNewMessagesDivider:{type:Boolean,required:!0},showFooter:{type:Boolean,required:!0},acceptedFiles:{type:String,required:!0},textFormatting:{type:Boolean,required:!0},linkOptions:{type:Object,required:!0},loadingRooms:{type:Boolean,required:!0},messageInTransit:{type:Boolean,required:!0},textareaActionEnabled:{type:Boolean,required:!0},templatesText:{type:Array,default:null},toggleLabelsModal:{type:Function,default:()=>({})},templates:{type:Array,required:!0},unreadCounts:{type:Object,required:!0}},emits:["toggle-rooms-list","edit-message","send-message","delete-message","message-action-handler","fetch-messages","send-message-reaction","typing-message","open-file","textarea-action-handler","carousel-handler","open-forward-modal","open-template-modal","toggle-menu-bar","redirect-to-hubspot"],data(){return{disableFooter:!1,sendIcon:Ce,sendIconDisabled:we,message:"",editedMessage:{},messageReply:null,infiniteState:null,loadingMessages:!0,loadingMoreMessages:!1,files:[],fileDialog:!1,emojiOpened:!1,hideOptions:!0,scrollIcon:!1,scrollMessagesCount:0,newMessages:[],keepKeyboardOpen:!1,filteredEmojis:[],filteredUsersTag:[],selectedUsersTag:[],filteredTemplatesText:[],selectEmojiItem:null,selectUsersTagItem:null,selectTemplatesTextItem:null,activeUpOrDownEmojis:null,activeUpOrDownUsersTag:null,activeUpOrDownTemplatesText:null,textareaCursorPosition:null,cursorRangePosition:null,emojisDB:new he.W,recorder:this.initRecorder(),isRecording:!1,format:"mp3",showUploadModal:!1,msgSearchQuery:""}},computed:{room(){return this.rooms.find((e=>e.roomId===this.roomId))||{}},showNoMessages(){return this.room.roomId&&!this.messages.length&&!this.loadingMessages&&!this.loadingRooms},showNoRoom(){const e=!this.rooms.length&&!this.loadingRooms||!this.room.roomId&&!this.loadFirstRoom;return e&&(this.loadingMessages=!1),e},showMessagesStarted(){return this.messages.length&&this.messagesLoaded},isMessageEmpty(){return!this.files.length&&!this.message.trim()},recordedTime(){return new Date(1e3*this.recorder.duration).toISOString().substr(14,5)},shadowFooter(){return!!this.filteredEmojis.length||!!this.filteredUsersTag.length||!!this.filteredTemplatesText.length||!!this.files.length||!!this.messageReply}},watch:{message(e){this.getTextareaRef().value=e},loadingMessages(e){e?this.infiniteState=null:(this.infiniteState&&this.infiniteState.loaded(),this.focusTextarea(!0))},room:{immediate:!0,handler(e,t){!e.roomId||t&&e.roomId===t.roomId||this.onRoomChanged()}},roomMessage:{immediate:!0,handler(e){e&&(this.message=this.roomMessage)}},messages:{deep:!0,handler(e,t){this.newMessages=[],e.forEach(((e,t)=>{this.showNewMessagesDivider&&e.new&&!e.fromMe&&this.newMessages.push({_id:e._id,index:t})})),t?.length===e?.length-1&&(this.newMessages=[]),this.infiniteState&&this.infiniteState.loaded(),this.getLastMessageFromOther(),setTimeout((()=>this.loadingMoreMessages=!1))}},messagesLoaded(e){e&&(this.loadingMessages=!1),this.infiniteState&&this.infiniteState.complete()}},mounted(){this.newMessages=[];const e=xo();this.getTextareaRef().addEventListener("keyup",To((t=>{"Enter"!==t.key||t.shiftKey||this.fileDialog||(e?(this.message=this.message+"\n",setTimeout((()=>this.onChangeInput()))):this.filteredEmojis.length||this.filteredUsersTag.length||this.filteredTemplatesText.length||this.sendMessage()),setTimeout((()=>{this.updateFooterList("@"),this.updateFooterList(":"),this.updateFooterList("/")}),60)})),50),this.getTextareaRef().addEventListener("click",(()=>{e&&(this.keepKeyboardOpen=!0),this.updateFooterList("@"),this.updateFooterList(":"),this.updateFooterList("/")})),this.getTextareaRef().addEventListener("blur",(()=>{this.resetFooterList(),e&&setTimeout((()=>this.keepKeyboardOpen=!1))})),this.getLastMessageFromOther()},beforeDestroy(){this.stopRecorder()},methods:{forwardTemplateMsg(e){this.$emit("add-template-msg",e)},getLastMessageFromOther(){if(!Array.isArray(this.messages))return null;const e=[...this.messages].reverse(),t=e.find((e=>0===e.fromMe)),s=new Date;if(t){const e=t.time,o=new Date(1e3*e),a=(s-o)/36e5;this.disableFooter=a>24}else this.disableFooter=!0;this.disableFooter&&this.$refs.roomTextarea&&this.$refs.roomTextarea.blur()},handleMessageSearch(e){this.msgSearchQuery=e},getTextareaRef(){return this.$refs.roomTextarea},touchStart(e){if(!this.singleRoom&&1===e.changedTouches.length){const t=e.changedTouches[0].clientX,s=e.changedTouches[0].clientY;addEventListener("touchend",(e=>this.touchEnd(e,t,s)),{once:!0})}},touchEnd(e,t,s){if(1===e.changedTouches.length){const o=e.changedTouches[0].clientX,a=e.changedTouches[0].clientY,i=o-t>100,n=Math.abs(a-s)>50;i&&!n&&this.$emit("toggle-rooms-list")}},onRoomChanged(){this.loadingMessages=!0,this.scrollIcon=!1,this.scrollMessagesCount=0,this.resetMessage(!0,!0),this.roomMessage&&(this.message=this.roomMessage,setTimeout((()=>this.onChangeInput()))),!this.messages.length&&this.messagesLoaded&&(this.loadingMessages=!1);const e=this.$watch((()=>this.messages),(t=>{if(!t||!t.length)return;const s=this.$refs.scrollContainer;s&&(e(),setTimeout((()=>{s.scrollTo({top:s.scrollHeight}),this.loadingMessages=!1})))}))},onMessageAdded({message:e,index:t,ref:s}){if(t!==this.messages.length-1)return;const o=s.offsetHeight+60;setTimeout((()=>{this.getBottomScroll(this.$refs.scrollContainer)<o||1===e.fromMe?this.scrollToBottom():(this.scrollIcon=!0,this.scrollMessagesCount++)}))},onContainerScroll(e){if(this.hideOptions=!0,!e.target)return;const t=this.getBottomScroll(e.target);t<60&&(this.scrollMessagesCount=0),this.scrollIcon=t>500||this.scrollMessagesCount},updateFooterList(e){if(!this.getTextareaRef())return;if("@"===e&&(!this.room.users||this.room.users.length<=2))return;if("/"===e&&!this.templatesText)return;if(this.textareaCursorPosition===this.getTextareaRef().selectionStart)return;this.textareaCursorPosition=this.getTextareaRef().selectionStart;let t=this.textareaCursorPosition;while(t>0&&this.message.charAt(t-1)!==e&&" "!==this.message.charAt(t-1))t--;const s=this.message.charAt(t-2),o=!s.match(/^[0-9a-zA-Z]+$/);if(this.message.charAt(t-1)!==e||s&&" "!==s&&!o)this.resetFooterList(e);else{const s=this.message.substring(t,this.textareaCursorPosition);":"===e?this.updateEmojis(s):"@"===e?this.updateShowUsersTag(s):"/"===e&&this.updateShowTemplatesText(s)}},getCharPosition(e){const t=this.getTextareaRef().selectionStart;let s=t;while(s>0&&this.message.charAt(s-1)!==e)s--;let o=s;while(this.message.charAt(o)&&this.message.charAt(o).trim())o++;return{position:s,endPosition:o}},async updateEmojis(e){if(!e)return;const t=await this.emojisDB.getEmojiBySearchQuery(e);this.filteredEmojis=t.map((e=>e.unicode))},selectEmoji(e){if(this.selectEmojiItem=!1,!e)return;const{position:t,endPosition:s}=this.getCharPosition(":");this.message=this.message.substr(0,t-1)+e+this.message.substr(s,this.message.length-1),this.cursorRangePosition=t,this.focusTextarea()},updateShowUsersTag(e){this.filteredUsersTag=_o(this.room.users,"username",e,!0).filter((e=>e._id!==this.currentUserId))},selectUserTag(e,t=!1){if(this.selectUsersTagItem=!1,!e)return;const{position:s,endPosition:o}=this.getCharPosition("@"),a=this.message.substr(o,o).length?"":" ";this.message=this.message.substr(0,s)+e.username+a+this.message.substr(o,this.message.length-1),this.selectedUsersTag=[...this.selectedUsersTag,{...e}],t||(this.cursorRangePosition=s+e.username.length+a.length+1),this.focusTextarea()},updateShowTemplatesText(e){this.filteredTemplatesText=_o(this.templatesText,"tag",e,!0)},selectTemplateText(e){if(this.selectTemplatesTextItem=!1,!e)return;const{position:t,endPosition:s}=this.getCharPosition("/"),o=this.message.substr(s,s).length?"":" ";this.message=this.message.substr(0,t-1)+e.text+o+this.message.substr(s,this.message.length-1),this.cursorRangePosition=t+e.text.length+o.length+1,this.focusTextarea()},updateActiveUpOrDown(e,t){this.filteredEmojis.length?(this.activeUpOrDownEmojis=t,e.preventDefault()):this.filteredUsersTag.length?(this.activeUpOrDownUsersTag=t,e.preventDefault()):this.filteredTemplatesText.length&&(this.activeUpOrDownTemplatesText=t,e.preventDefault())},selectItem(){this.filteredEmojis.length?this.selectEmojiItem=!0:this.filteredUsersTag.length?this.selectUsersTagItem=!0:this.filteredTemplatesText.length&&(this.selectTemplatesTextItem=!0)},resetFooterList(e=null){":"===e?this.filteredEmojis=[]:"@"===e?this.filteredUsersTag=[]:("/"===e||(this.filteredEmojis=[],this.filteredUsersTag=[]),this.filteredTemplatesText=[]),this.textareaCursorPosition=null},escapeTextarea(){this.filteredEmojis.length?this.filteredEmojis=[]:this.filteredUsersTag.length?this.filteredUsersTag=[]:this.filteredTemplatesText.length?this.filteredTemplatesText=[]:this.resetMessage()},resetMessage(e=!1,t=!1){t||this.$emit("typing-message",null),this.selectedUsersTag=[],this.resetFooterList(),this.resetTextareaSize(),this.message="",this.editedMessage={},this.messageReply=null,this.files=[],this.emojiOpened=!1,this.preventKeyboardFromClosing(),setTimeout((()=>this.focusTextarea(e)))},resetTextareaSize(){this.getTextareaRef()&&(this.getTextareaRef().style.height="20px")},focusTextarea(e){xo()&&e||this.getTextareaRef()&&(this.getTextareaRef().focus(),this.cursorRangePosition&&setTimeout((()=>{this.getTextareaRef().setSelectionRange(this.cursorRangePosition,this.cursorRangePosition),this.cursorRangePosition=null})))},preventKeyboardFromClosing(){this.keepKeyboardOpen&&this.getTextareaRef().focus()},sendMessage(){let e=this.message.trim();if(!this.files.length&&!e)return;this.selectedUsersTag.forEach((t=>{e=e.replace(`@${t.username}`,`<usertag>${t._id}</usertag>`)}));const t=this.files.length?this.files:null;this.editedMessage._id?(this.editedMessage.content!==e||this.editedMessage.files?.length||this.files.length)&&this.$emit("edit-message",{messageId:this.editedMessage._id,newContent:e,files:t,replyMessage:this.messageReply,usersTag:this.selectedUsersTag}):this.$emit("send-message",{content:e,files:t,replyMessage:this.messageReply,usersTag:this.selectedUsersTag}),this.resetMessage(!0)},loadMoreMessages(e){this.loadingMessages?this.infiniteState=e:setTimeout((()=>{if(!this.loadingMoreMessages){if(this.messagesLoaded||!this.room.roomId)return e.complete();this.infiniteState=e,this.$emit("fetch-messages"),this.loadingMoreMessages=!0}}),So()?500:0)},messageActionHandler({action:e,message:t}){switch(e.name){case"replyMessage":return this.replyMessage(t);case"editMessage":return this.editMessage(t);case"deleteMessage":return this.$emit("delete-message",t);default:return this.$emit("message-action-handler",{action:e,message:t})}},sendMessageReaction(e){this.$emit("send-message-reaction",e)},replyMessage(e){this.editedMessage={},this.messageReply=e,this.focusTextarea()},editMessage(e){this.resetMessage(),this.editedMessage={...e};let t=e.content;const s=t,o="<usertag>",a="</usertag>",i=[...t.matchAll(new RegExp(o,"gi"))].map((e=>e.index));i.forEach((e=>{const i=s.substring(e+o.length,s.indexOf(a,e)),n=this.room.users.find((e=>e._id===i));t=t.replace(`${o}${i}${a}`,`@${n?.username||"unknown"}`),this.selectUserTag(n,!0)})),this.message=t,e.files&&(this.files=[...e.files]),setTimeout((()=>this.resizeTextarea()))},getBottomScroll(e){const{scrollHeight:t,clientHeight:s,scrollTop:o}=e;return t-s-o},scrollToBottom(){setTimeout((()=>{const e=this.$refs.scrollContainer;e.classList.add("vac-scroll-smooth"),e.scrollTo({top:e.scrollHeight,behavior:"smooth"}),setTimeout((()=>e.classList.remove("vac-scroll-smooth")))}),50)},onChangeInput:To((function(e){this.message=e?.target?.value,this.keepKeyboardOpen=!0,this.resizeTextarea(),this.$emit("typing-message",this.message)}),100),resizeTextarea(){const e=this.getTextareaRef();if(!e)return;const t=window.getComputedStyle(e,null).getPropertyValue("padding-top").replace("px","");e.style.height=0,e.style.height=e.scrollHeight-2*t+"px"},addEmoji(e){this.message+=e.unicode,this.focusTextarea(!0)},launchFilePicker(){this.$refs.file.value="",this.toggleUploadModal()},onPasteImage(e){const t=[...e.clipboardData?.items];t&&t.forEach((e=>{if(e.type.includes("image")){const t=e.getAsFile();this.onFileChange([t])}}))},async onFileChange(e){this.fileDialog=!0,this.focusTextarea();const t=this.files.map((e=>e.name)),s=Array.from(e).filter((e=>e.type)),o=[...new Set(s)];o.forEach((async(e,s)=>{try{const a=URL.createObjectURL(e),i=await fetch(a).then((e=>e.blob())),n=e.name.lastIndexOf("."),r=e.name.substring(0,n);t.includes(r)||this.files.push({blob:i,name:r,size:e.size,extension:e.name.substring(n+1),localUrl:a,url:a,rawData:e,loading:!0,error:!1}),s===o.length-1&&setTimeout((()=>{this.fileDialog=!1,this.showUploadModal=!1}),500)}catch(a){setTimeout((()=>{this.fileDialog=!1,this.showUploadModal=!1}),500)}}))},removeFile(e){this.files.splice(e,1),this.focusTextarea()},initRecorder(){return this.isRecording=!1,new Io({bitRate:this.audioBitRate,sampleRate:this.audioSampleRate,beforeRecording:null,afterRecording:null,pauseRecording:null,micFailed:this.micFailed})},micFailed(){this.isRecording=!1,this.recorder=this.initRecorder()},toggleRecorder(e){if(this.isRecording=e,this.recorder.isRecording)try{this.recorder.stop();const e=this.recorder.records[0];this.files.push({blob:e.blob,name:`audio.${this.format}`,size:e.blob.size,duration:e.duration,type:e.blob.type,audio:!0,localUrl:URL.createObjectURL(e.blob)}),this.recorder=this.initRecorder(),this.sendMessage()}catch{setTimeout((()=>this.stopRecorder()),100)}else setTimeout((()=>this.recorder.start()),200)},stopRecorder(){if(this.recorder.isRecording)try{this.recorder.stop(),this.recorder=this.initRecorder()}catch{setTimeout((()=>this.stopRecorder()),100)}},openFile({message:e,file:t}){this.$emit("open-file",{message:e,file:t})},textareaActionHandler(){this.$emit("textarea-action-handler",this.message)},addTemplate(e){this.message=e,setTimeout((()=>this.resizeTextarea())),this.focusTextarea(!0)},toggleUploadModal(){this.showUploadModal=!this.showUploadModal},handleUpload(e){e.dataTransfer?this.onFileChange(e.dataTransfer.files):this.$refs.file.click()},carouselHandler(e){this.$emit("carousel-handler",e)},toggleErrorModal(){this.$emit("toggle-error-modal")},replyMsgHandler(e){const t=document.getElementById(`${e.replyMessage._id}-child`);t&&(t.style.border="2px solid orangered",t.scrollIntoView(),setTimeout((()=>t.style.border="none"),500))}}},Ro=Fo,Eo=(0,u.A)(Ro,ce,de,!1,null,null,null),Lo=Eo.exports,Bo={ROOMS_EMPTY:"No chats",ROOM_EMPTY:"No chat selected",NEW_MESSAGES:"New Messages",MESSAGE_DELETED:"This message was deleted",MESSAGES_EMPTY:"No messages",CONVERSATION_STARTED:"Conversation started on:",TYPE_MESSAGE:"Type message",SEARCH:"Search",IS_ONLINE:"is online",LAST_SEEN:"last seen ",IS_TYPING:"is writing..."};const Oo={light:{general:{color:"#0a0a0a",backgroundInput:"#fff",colorPlaceholder:"#9ca6af",colorCaret:"#1976d2",colorSpinner:"#333",borderStyle:"1px solid #e1e4e8",backgroundScrollIcon:"#fff"},container:{border:"none",borderRadius:"4px",boxShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)"},header:{background:"#fff",colorRoomName:"#0a0a0a",colorRoomInfo:"#9ca6af"},footer:{background:"#f8f9fa",borderStyleInput:"1px solid #e1e4e8",borderInputSelected:"#1976d2",backgroundReply:"#e5e5e6",backgroundTagActive:"#e5e5e6",backgroundTag:"#f8f9fa"},content:{background:"#f8f9fa"},sidemenu:{background:"#fff",backgroundHover:"#f6f6f6",backgroundActive:"#e5effa",colorActive:"#1976d2",borderColorSearch:"#e1e5e8"},dropdown:{background:"#fff",backgroundHover:"#f6f6f6"},message:{background:"#fff",backgroundMe:"#ccf2cf",color:"#0a0a0a",colorStarted:"#9ca6af",backgroundDeleted:"#dadfe2",colorDeleted:"#757e85",colorUsername:"#9ca6af",colorTimestamp:"#828c94",backgroundDate:"#e5effa",colorDate:"#505a62",backgroundSystem:"#e5effa",colorSystem:"#505a62",backgroundMedia:"rgba(0, 0, 0, 0.15)",backgroundReply:"rgba(0, 0, 0, 0.08)",colorReplyUsername:"#0a0a0a",colorReply:"#6e6e6e",colorTag:"#0d579c",backgroundImage:"#ddd",colorNewMessages:"#34b7f1",backgroundScrollCounter:"#0696c7",colorScrollCounter:"#fff",backgroundReaction:"#eee",borderStyleReaction:"1px solid #eee",backgroundReactionHover:"#fff",borderStyleReactionHover:"1px solid #ddd",colorReactionCounter:"#0a0a0a",backgroundReactionMe:"#cfecf5",borderStyleReactionMe:"1px solid #3b98b8",backgroundReactionHoverMe:"#cfecf5",borderStyleReactionHoverMe:"1px solid #3b98b8",colorReactionCounterMe:"#0b59b3",backgroundAudioRecord:"#eb4034",backgroundAudioLine:"rgba(0, 0, 0, 0.15)",backgroundAudioProgress:"#455247",backgroundAudioProgressSelector:"#455247",colorFileExtension:"#757e85"},markdown:{background:"rgba(239, 239, 239, 0.7)",border:"rgba(212, 212, 212, 0.9)",color:"#e01e5a",colorMulti:"#0a0a0a"},room:{colorUsername:"#0a0a0a",colorMessage:"#67717a",colorTimestamp:"#a2aeb8",colorStateOnline:"#4caf50",colorStateOffline:"#9ca6af",backgroundCounterBadge:"#34b7f1",colorCounterBadge:"#fff"},emoji:{background:"#fff"},icons:{search:"#9ca6af",add:"#1976d2",toggle:"#0a0a0a",menu:"#0a0a0a",close:"#9ca6af",closeImage:"#fff",file:"#1976d2",paperclip:"#1976d2",closeOutline:"#000",send:"#1976d2",sendDisabled:"#9ca6af",emoji:"#1976d2",emojiReaction:"rgba(0, 0, 0, 0.3)",document:"#1976d2",pencil:"#9e9e9e",checkmark:"#9e9e9e",checkmarkSeen:"#0696c7",eye:"#fff",dropdownMessage:"#fff",dropdownMessageBackground:"rgba(0, 0, 0, 0.25)",dropdownRoom:"#9e9e9e",dropdownScroll:"#0a0a0a",microphone:"#1976d2",audioPlay:"#455247",audioPause:"#455247",audioCancel:"#eb4034",audioConfirm:"#1ba65b"}},dark:{general:{color:"#fff",backgroundInput:"#202223",colorPlaceholder:"#596269",colorCaret:"#fff",colorSpinner:"#fff",borderStyle:"none",backgroundScrollIcon:"#fff"},container:{border:"none",borderRadius:"4px",boxShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)"},header:{background:"#181a1b",colorRoomName:"#fff",colorRoomInfo:"#9ca6af"},footer:{background:"#131415",borderStyleInput:"none",borderInputSelected:"#1976d2",backgroundReply:"#1b1c1c",backgroundTagActive:"#1b1c1c",backgroundTag:"#131415"},content:{background:"#131415"},sidemenu:{background:"#181a1b",backgroundHover:"#202224",backgroundActive:"#151617",colorActive:"#fff",borderColorSearch:"#181a1b"},dropdown:{background:"#2a2c33",backgroundHover:"#26282e"},message:{background:"#22242a",backgroundMe:"#1f7e80",color:"#fff",colorStarted:"#9ca6af",backgroundDeleted:"#1b1c21",colorDeleted:"#a2a5a8",colorUsername:"#b3bac9",colorTimestamp:"#ebedf2",backgroundDate:"rgba(0, 0, 0, 0.3)",colorDate:"#bec5cc",backgroundSystem:"rgba(0, 0, 0, 0.3)",colorSystem:"#bec5cc",backgroundMedia:"rgba(0, 0, 0, 0.18)",backgroundReply:"rgba(0, 0, 0, 0.18)",colorReplyUsername:"#fff",colorReply:"#d6d6d6",colorTag:"#f0c60a",backgroundImage:"#ddd",colorNewMessages:"#fff",backgroundScrollCounter:"#1976d2",colorScrollCounter:"#fff",backgroundReaction:"none",borderStyleReaction:"none",backgroundReactionHover:"#202223",borderStyleReactionHover:"none",colorReactionCounter:"#fff",backgroundReactionMe:"#4e9ad1",borderStyleReactionMe:"none",backgroundReactionHoverMe:"#4e9ad1",borderStyleReactionHoverMe:"none",colorReactionCounterMe:"#fff",backgroundAudioRecord:"#eb4034",backgroundAudioLine:"rgba(255, 255, 255, 0.15)",backgroundAudioProgress:"#b7d4d3",backgroundAudioProgressSelector:"#b7d4d3",colorFileExtension:"#a2a5a8"},markdown:{background:"rgba(239, 239, 239, 0.7)",border:"rgba(212, 212, 212, 0.9)",color:"#e01e5a",colorMulti:"#0a0a0a"},room:{colorUsername:"#fff",colorMessage:"#6c7278",colorTimestamp:"#6c7278",colorStateOnline:"#4caf50",colorStateOffline:"#596269",backgroundCounterBadge:"#1976d2",colorCounterBadge:"#fff"},emoji:{background:"#343740"},icons:{search:"#596269",add:"#fff",toggle:"#fff",menu:"#fff",close:"#9ca6af",closeImage:"#fff",file:"#1976d2",paperclip:"#fff",closeOutline:"#fff",send:"#fff",sendDisabled:"#646a70",emoji:"#fff",emojiReaction:"#fff",document:"#1976d2",pencil:"#ebedf2",checkmark:"#ebedf2",checkmarkSeen:"#f0d90a",eye:"#fff",dropdownMessage:"#fff",dropdownMessageBackground:"rgba(0, 0, 0, 0.25)",dropdownRoom:"#fff",dropdownScroll:"#0a0a0a",microphone:"#fff",audioPlay:"#b7d4d3",audioPause:"#b7d4d3",audioCancel:"#eb4034",audioConfirm:"#1ba65b"}}},Do=({general:e,container:t,header:s,footer:o,sidemenu:a,content:i,dropdown:n,message:r,markdown:l,room:c,emoji:d,icons:u})=>({"--chat-color":e.color,"--chat-bg-color-input":e.backgroundInput,"--chat-color-spinner":e.colorSpinner,"--chat-color-placeholder":e.colorPlaceholder,"--chat-color-caret":e.colorCaret,"--chat-border-style":e.borderStyle,"--chat-bg-scroll-icon":e.backgroundScrollIcon,"--chat-container-border":t.border,"--chat-container-border-radius":t.borderRadius,"--chat-container-box-shadow":t.boxShadow,"--chat-header-bg-color":s.background,"--chat-header-color-name":s.colorRoomName,"--chat-header-color-info":s.colorRoomInfo,"--chat-footer-bg-color":o.background,"--chat-border-style-input":o.borderStyleInput,"--chat-border-color-input-selected":o.borderInputSelected,"--chat-footer-bg-color-reply":o.backgroundReply,"--chat-footer-bg-color-tag-active":o.backgroundTagActive,"--chat-footer-bg-color-tag":o.backgroundTag,"--chat-content-bg-color":i.background,"--chat-sidemenu-bg-color":a.background,"--chat-sidemenu-bg-color-hover":a.backgroundHover,"--chat-sidemenu-bg-color-active":a.backgroundActive,"--chat-sidemenu-color-active":a.colorActive,"--chat-sidemenu-border-color-search":a.borderColorSearch,"--chat-dropdown-bg-color":n.background,"--chat-dropdown-bg-color-hover":n.backgroundHover,"--chat-message-bg-color":r.background,"--chat-message-bg-color-me":r.backgroundMe,"--chat-message-color-started":r.colorStarted,"--chat-message-bg-color-deleted":r.backgroundDeleted,"--chat-message-color-deleted":r.colorDeleted,"--chat-message-color-username":r.colorUsername,"--chat-message-color-timestamp":r.colorTimestamp,"--chat-message-bg-color-date":r.backgroundDate,"--chat-message-color-date":r.colorDate,"--chat-message-bg-color-system":r.backgroundSystem,"--chat-message-color-system":r.colorSystem,"--chat-message-color":r.color,"--chat-message-bg-color-media":r.backgroundMedia,"--chat-message-bg-color-reply":r.backgroundReply,"--chat-message-color-reply-username":r.colorReplyUsername,"--chat-message-color-reply-content":r.colorReply,"--chat-message-color-tag":r.colorTag,"--chat-message-bg-color-image":r.backgroundImage,"--chat-message-color-new-messages":r.colorNewMessages,"--chat-message-bg-color-scroll-counter":r.backgroundScrollCounter,"--chat-message-color-scroll-counter":r.colorScrollCounter,"--chat-message-bg-color-reaction":r.backgroundReaction,"--chat-message-border-style-reaction":r.borderStyleReaction,"--chat-message-bg-color-reaction-hover":r.backgroundReactionHover,"--chat-message-border-style-reaction-hover":r.borderStyleReactionHover,"--chat-message-color-reaction-counter":r.colorReactionCounter,"--chat-message-bg-color-reaction-me":r.backgroundReactionMe,"--chat-message-border-style-reaction-me":r.borderStyleReactionMe,"--chat-message-bg-color-reaction-hover-me":r.backgroundReactionHoverMe,"--chat-message-border-style-reaction-hover-me":r.borderStyleReactionHoverMe,"--chat-message-color-reaction-counter-me":r.colorReactionCounterMe,"--chat-message-bg-color-audio-record":r.backgroundAudioRecord,"--chat-message-bg-color-audio-line":r.backgroundAudioLine,"--chat-message-bg-color-audio-progress":r.backgroundAudioProgress,"--chat-message-bg-color-audio-progress-selector":r.backgroundAudioProgressSelector,"--chat-message-color-file-extension":r.colorFileExtension,"--chat-markdown-bg":l.background,"--chat-markdown-border":l.border,"--chat-markdown-color":l.color,"--chat-markdown-color-multi":l.colorMulti,"--chat-room-color-username":c.colorUsername,"--chat-room-color-message":c.colorMessage,"--chat-room-color-timestamp":c.colorTimestamp,"--chat-room-color-online":c.colorStateOnline,"--chat-room-color-offline":c.colorStateOffline,"--chat-room-bg-color-badge":c.backgroundCounterBadge,"--chat-room-color-badge":c.colorCounterBadge,"--chat-emoji-bg-color":d.background,"--chat-icon-color-search":u.search,"--chat-icon-color-add":u.add,"--chat-icon-color-toggle":u.toggle,"--chat-icon-color-menu":u.menu,"--chat-icon-color-close":u.close,"--chat-icon-color-close-image":u.closeImage,"--chat-icon-color-file":u.file,"--chat-icon-color-paperclip":u.paperclip,"--chat-icon-color-close-outline":u.closeOutline,"--chat-icon-color-send":u.send,"--chat-icon-color-send-disabled":u.sendDisabled,"--chat-icon-color-emoji":u.emoji,"--chat-icon-color-emoji-reaction":u.emojiReaction,"--chat-icon-color-document":u.document,"--chat-icon-color-pencil":u.pencil,"--chat-icon-color-checkmark":u.checkmark,"--chat-icon-color-checkmark-seen":u.checkmarkSeen,"--chat-icon-color-eye":u.eye,"--chat-icon-color-dropdown-message":u.dropdownMessage,"--chat-icon-bg-dropdown-message":u.dropdownMessageBackground,"--chat-icon-color-dropdown-room":u.dropdownRoom,"--chat-icon-color-dropdown-scroll":u.dropdownScroll,"--chat-icon-color-microphone":u.microphone,"--chat-icon-color-audio-play":u.audioPlay,"--chat-icon-color-audio-pause":u.audioPause,"--chat-icon-color-audio-cancel":u.audioCancel,"--chat-icon-color-audio-confirm":u.audioConfirm}),{roomsValidation:No,partcipantsValidation:Uo}=s(8900);var jo={name:"ChatContainer",components:{Room:Lo,AppCarousel:H,ErrorModal:K,SuccessModal:ee,ImageViewer:ne},props:{userData:{type:Object,required:!0},theme:{type:String,default:"light"},styles:{type:Object,default:()=>({})},singleRoom:{type:Boolean,default:!1},roomsListOpened:{type:Boolean,default:!0},textMessages:{type:Object,default:null},currentUserId:{type:[String,Number],default:""},rooms:{type:Array,default:()=>[]},loadingRooms:{type:Boolean,default:!1},roomsLoaded:{type:Boolean,default:!1},engagement:{type:Object,default:null},participants:{type:Object,default:null},loadingTab:{type:Boolean,required:!1},showAddToHubspot:{type:Boolean,required:!1},setContactObjects:{type:Boolean,required:!1},showParticipants:{type:Boolean,required:!0},roomId:{type:[String,Number],default:null},loadFirstRoom:{type:Boolean,default:!0},isMsgFetched:{type:Boolean,default:!0},messages:{type:Array,default:()=>[]},messagesLoaded:{type:Boolean,default:!1},roomActions:{type:Array,default:()=>[]},menuActions:{type:Array,default:()=>[]},messageActions:{type:Array,default:()=>[{name:"replyMessage",title:"Reply"}]},showSearch:{type:Boolean,default:!0},showAddRoom:{type:Boolean,default:!0},showSendIcon:{type:Boolean,default:!0},showFiles:{type:Boolean,default:!0},showAudio:{type:Boolean,default:!0},audioBitRate:{type:Number,default:128},audioSampleRate:{type:Number,default:44100},showEmojis:{type:Boolean,default:!0},showReactionEmojis:{type:Boolean,default:!0},showNewMessagesDivider:{type:Boolean,default:!0},showFooter:{type:Boolean,default:!0},textFormatting:{type:Boolean,default:!0},linkOptions:{type:Object,default:()=>({disabled:!1,target:"_blank",rel:null})},textareaActionEnabled:{type:Boolean,default:!1},roomMessage:{type:String,default:""},acceptedFiles:{type:String,default:"*"},templatesText:{type:Array,default:null},selectedRoom:{type:Object,default:()=>{}},labels:{type:Array,required:!0},showLabels:{type:Boolean,required:!0},roomLabels:{type:Array,required:!0},templates:{type:Array,required:!0},assigningLabel:{type:Boolean,required:!0},toggleErrorModal:{type:Function,required:!0},toggleSuccessModal:{type:Function,required:!0},unreadCounts:{type:Object,required:!0},sidebarVisible:{type:Boolean,required:!0},showErrorModal:{type:Boolean,required:!0},requests:{type:Array,required:!0},request:{type:Object,default:()=>{}},showCreateModal:{type:Boolean,required:!1},showSuccessModal:{type:Boolean,required:!0},saveKey:{type:Boolean,required:!1},messageInTransit:{type:Boolean,required:!0},errorMessage:{type:String,default:""},successMessage:{type:Object,default:()=>({heading:"Success",content:"Successful!"})}},emits:["toggle-rooms-list","fetch-messages","send-message","edit-message","delete-message","open-file","message-action-handler","send-message-reaction","typing-message","textarea-action-handler","add-room","room-action-handler","chat-label-handler","toggle-menu-bar","toggle-labels-modal","close-sidebar","redirect-to-hubspot"],data(){return{room:this.selectedRoom||{},carouselData:[],forwardMessage:{},loadingMoreRooms:!1,showRoomsList:!0,isMobile:!1,showCarousel:!1,showProfile:!1,previewImage:!1,previewMessage:{},startchat:!1}},computed:{t(){return{...Bo,...this.textMessages}},cssVars(){const e=Oo[this.theme],t={};return Object.keys(e).map((s=>{t[s]={...e[s],...this.styles[s]||{}}})),Do(t)}},watch:{rooms:{immediate:!0,deep:!0,handler(e,t){if(e[0]&&e.find((e=>e.roomId===this.room.roomId))||(this.showRoomsList=!0),!this.loadingMoreRooms&&this.loadFirstRoom&&e[0]&&(!t||e.length!==t.length))if(this.roomId){const t=e.find((e=>e.roomId===this.roomId))||{};this.fetchRoom({room:t})}else this.room&&0!==Object.keys(this.room).length?this.fetchRoom({room:this.room}):this.showRoomsList=!0}},loadingRooms(e){e&&(this.room={})},roomId:{immediate:!0,handler(e,t){if(e&&!this.loadingRooms&&this.rooms.length){const t=this.rooms.find((t=>t.roomId===e));this.fetchRoom({room:t})}else t&&!e&&(this.room={})}},room(e){e&&0!==Object.entries(e).length&&(No(e),e.users.forEach((e=>{Uo(e)})))},roomsListOpened(e){this.showRoomsList=e},sidebarVisible(e){e&&this.showProfile&&(this.showProfile=!1)}},created(){this.fetchMessages()},methods:{closeImageViewer(){this.previewImage=!1,this.previewMessage={}},forwardTemplateMsg(e){this.$emit("add-template-msg",e)},fetchMessages(e){const t=le().CancelToken.source();this.$emit("fetch-messages",{room:this.room,options:e,source:t})},sendMessage(e){this.$emit("send-message",{...e,roomId:this.room.roomId,phone:this.room.phone})},editMessage(e){this.$emit("edit-message",{...e,roomId:this.room.roomId})},deleteMessage(e){this.$emit("delete-message",{message:e,roomId:this.room.roomId})},openFile({message:e,file:t}){if("preview"===t.action)return this.previewImage=!0,void(this.previewMessage=t.file);this.$emit("open-file",{message:e,file:t})},roomActionHandler({action:e,did:t}){this.$emit("room-action-handler",{action:e,did:t})},sendMessageReaction(e){this.$emit("send-message-reaction",{...e,roomId:this.room.roomId})},typingMessage(e){this.$emit("typing-message",{message:e,roomId:this.room.roomId})},textareaActionHandler(e){this.$emit("textarea-action-handler",{message:e,roomId:this.room.roomId})},carouselHandler(e){const t=e.map((e=>({id:e.name,name:e.name+"."+e.extension,big:e.url,thumb:e.url,timestamp:e.timestamp,date:e.date,username:e.username,msg_id:e._id})));this.carouselData=t,this.showCarousel=!0},closeCarousel(){this.showCarousel=!1},toggleProfile(){this.showProfile=!this.showProfile},startChat(){this.startchat=!this.startchat},closeChat(){this.startchat=!this.startchat}}},Po=jo,Ho=(0,u.A)(Po,S,T,!1,null,null,null),Vo=Ho.exports,qo=s(6479),Go=s.n(qo),Qo=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-spinner",class:{"on-page":e.onPage}},[e._m(0)])},zo=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"loader-inner ball-pulse"},[t("div"),t("div"),t("div")])}],Yo={name:"Spinner",props:["onPage"]},Ko=Yo,Jo=(0,u.A)(Ko,Qo,zo,!1,null,null,null),Xo=Jo.exports;const Zo=e=>{let t=new Date(1e3*e);return t.toLocaleString("en-US",{hour:"numeric",minute:"numeric",hour12:!0})},Wo=()=>Math.random().toString(36).replace(/[^a-z]+/g,"").substr(2,10),$o=e=>{const t=["png","jpg","jpeg","webp","svg","gif"];if(e&&e.extension)return t.some((t=>e.extension.toLowerCase().includes(t)))},ea=(e,t="")=>{if(!e)return;const s=new Date(1e3*e);if("HH:mm"===t)return`${ta(s.getHours(),2)}:${ta(s.getMinutes(),2)}`;if("DD MMMM YYYY"===t){const e={month:"long",year:"numeric",day:"numeric"};return`${new Intl.DateTimeFormat("en-GB",e).format(s)}`}if("DD/MM/YY"===t){const e={month:"numeric",year:"numeric",day:"numeric"};return`${new Intl.DateTimeFormat("en-GB",e).format(s)}`}if("DD MMMM, HH:mm"===t){const e={month:"long",day:"numeric"};return`${new Intl.DateTimeFormat("en-GB",e).format(s)}, ${ta(s.getHours(),2)}:${ta(s.getMinutes(),2)}`}return s},ta=(e,t)=>String(e).padStart(t,"0"),sa=(e,t)=>e.getFullYear()===t.getFullYear()&&e.getMonth()===t.getMonth()&&e.getDate()===t.getDate(),oa=e=>e?new Date(e).toLocaleDateString("en-GB",{day:"numeric",month:"short",year:"numeric"}):(new Date).toLocaleDateString("en-GB",{day:"numeric",month:"short",year:"numeric"});var aa={name:"Home",components:{ChatWindow:Vo,Spinner:Xo,Info:ze},data(){return{saveKey:!1,requests:[],request:null,showErrorModal:!1,showSuccessModal:!1,assigningLabel:!1,showCreateModal:!1,showAddToHubspot:!1,showParticipants:!0,setContactObjects:!1,avatar:Ze,templates:[],labels:[],engagement:{},participants:{},loadingTab:!1,roomLabels:[],showLabels:!1,selectedRoom:{},roomsLoaded:!1,loadingRooms:!0,rooms:[],messages:[],dataLoaded:!1,currentUserId:"",currentChatId:null,messagesLoaded:!1,user_id:null,username:null,portal_id:null,accountPhone:null,origin:"",errorMessagwhatshive_logoe:"",successMessage:{},messageInTransit:!1,isMsgFetched:!1,errorMessage:"",fetchMsgData:[],userId:null,objectId:null,phone:null,isAdmin:!1,isPermission:!1,loader:!0,noNeedToPermission:!1}},computed:{...(0,n.aH)(["unreadCounts","menuBarShow"])},created(){const e=this.$route.params.id,t=this.$store.state.userData;this.userData={username:t.accountUser},this.user_id=t.user_id,this.portal_id=t.portal_id,this.accountPhone=t.accountPhone,this.username=t.accountUser,this.origin=t.origin||"https://app.hubspot.com";var s=window.location.href,o=new URL(s);this.userId=o.searchParams.get("user_id"),this.objectId=o.searchParams.get("objectId")||null,this.phone=o.searchParams.get("phone")||null,this.fetchPermissions(),this.initialRequest(e),this.subscribe()},methods:{...(0,n.PY)(["setErrorApp","setErrorMsgApp","setUnreadCounts","toggleMenuBar"]),async fetchPermissions(){this.loader=!0;try{const e=new URLSearchParams(window.location.search),t=e.get("accountUser"),s=e.get("user_id"),o=await it.get(`api/users/${t}?user_id=${s}`);if(o.data.user){const e=o.data.user,t=e.permissions?.map((e=>e.replace(/\s+/g,"")));e.active&&(this.isPermission=t.includes("Chats")),this.isAdmin=e.admin,this.loader=!1}else this.noNeedToPermission=!0,this.loader=!1}catch(e){this.loader=!1,console.error("An error occurred while fetching permissions:",e)}},subscribe(){const e=`${this.portal_id}.${this.accountPhone}`;let t=new(Go())("8a63ec74543a8782f031",{forceTLS:!0,cluster:"eu",authEndpoint:`${at.baseURL}api/pusher/auth`,auth:{params:{user_id:this.user_id}}});t.connection.bind("connected",(()=>{}));const s=t.subscribe("private-"+e);s.bind("new-message",(e=>{const t=e.message;if(t.fromMe)return;const s=this.phone.replace("+",""),o=t.chatId.replace("+","");if(s===o){const e=this.formatMessage(t,!0);this.messages.push(e)}})),s.bind("new-status",(e=>{const t=e.status,s=this.messages.findIndex((e=>String(e.tmp_id)===String(t.id)||String(e._id)===String(t.id)));if(-1!==s){let e=this.messages[s];"delivered"==t.status&&(e.distributed=!0),"read"==t.status&&(e.distributed=!0,e.seen=!0),"failed"==t.status&&(e.failed=!0,e.reason=t?.status_reason),this.messages[s]=e,this.resetCounts()}else console.log("not found")}))},async initialRequest(e){try{const e=it.get(`api/v1/whatsapp/templates?user_id=${this.user_id}`),t=await Promise.all([e]);this.dataLoaded=!0,this.templates=t[0]?.data?.data||[],this.setErrorApp(!1)}catch(t){console.log(t),this.setErrorApp(!0)}},formatTimestamp(e){const t=new Date(1e3*e),s=sa(t,new Date)?"HH:mm":"DD/MM/YY",o=ea(e,s);return"HH:mm"===s?`Today, ${o}`:o},toggleLabelsModal(e){e?(this.roomLabels=e.labels.map((e=>e.id)),this.labels=this.labels.map((t=>({...t,roomId:e.roomId,selected:this.roomLabels.includes(t.id)})))):this.roomLabels=[],this.showLabels=!this.showLabels},roomActionHandler({action:e,did:t}){"pinChat"===e.name&&this.handlePin(t)},async handlePin(e){const t=this.rooms.findIndex((t=>t.did===e)),s=!this.rooms[t].pinned;try{const o=`api/dialog/${e}?user_id=${this.user_id}`,{data:a}=await it.post(o,{pinned:s});if(!a.ok)throw new Error;this.rooms[t].pinned=s}catch(o){this.showErrorModal=!0,console.log(o)}},addTemplateMsg(e){this.fetchMsgData.unshift(e.message),this.messages=[];let t=this.fetchMsgData.map((e=>this.formatMessage(e)));const s=this.combineFiles(t);s.forEach((e=>{this.messages.unshift(e)})),this.resetCounts()},async fetchMessages({room:e,options:t={},source:s}){this.isMsgFetched=!0,t.reset&&(this.messages=[],this.messagesLoaded=!1);let o=Math.round(Date.now()/1e3);const a=this.messages.length>0?this.messages[0].time:o;a===o&&(this.showAddToHubspot=!1,this.engagement={}),this.request={cancel:s.cancel,msg:"Loading...",message:"Request Cancelled"};const i=await it.get(`api/chat?user_id=${this.userId}&chatId=${this.phone}`,{cancelToken:s.token}).catch(this.logResponseErrors,this.isMsgFetche=!1);if(i){this.isMsgFetched=!1;const{data:e}=i;if("ok"===e.status){this.fetchMsgData=e.messages;const t=document.getElementsByClassName("text__highlight"),s=t[0]||null;if(setTimeout((()=>{s&&(document.getElementById("count").innerHTML=t.length)}),350),this.clearOldRequest("Success"),this.messagesLoaded=!0,0===e.messages.length)return;this.messages=[];let o=this.fetchMsgData.map((e=>this.formatMessage(e)));const a=this.combineFiles(o);a.forEach((e=>{this.messages.unshift(e)})),this.resetCounts(),this.setErrorApp(!1)}else console.log(e)}},logResponseErrors(e){console.log(e),console.log("Request cancelled")},clearOldRequest(e){this.request.msg=e,this.requests.push(this.request),this.request=null},combineFiles(e){const t=[];let s={};for(let[o,a]of e.entries()){if(a.files){const e=[{...a.files[0],timestamp:a.timestamp,distributed:a.distributed,seen:a.seen,date:a.date,username:a.username,_id:a._id}];if(0===o)s={...a,files:e};else{const o=t.length-1,i=t[o].files?.length-1;if(t[o].files&&a.fromMe===t[o].fromMe&&a.username===t[o].username&&!t[o].content&&$o(a.files[0])&&$o(t[o].files[i])&&!a.content){t[o].files.unshift(e[0]);continue}s={...a,files:e}}}else s=a;t.push(s),s={}}return t},formatMessage(e,t=!1){const s=e.quotedMsgId?{content:e.quotedFiles?"":e.quotedMsgBody,_id:e.quotedMsgId,username:e.name||e.senderName,files:e.quotedFiles}:null;return{_id:e.id,content:e?.caption?e.caption:e.body,username:e.name?e.name||e.senderName:"",date:oa(1e3*e.time),timestamp:Zo(e.time),failed:"failed"===e.status,reason:e.status_reason,saved:"sent"===e.status,distributed:"read"===e.status||"delivered"===e.status,seen:"read"===e.status,files:e.files,replyMessage:s,fromMe:t?0:e.fromMe,time:e.time}},getCounts(){const e=localStorage.getItem("hw-counts");return e?JSON.parse(e):{}},setCounts(e){this.setUnreadCounts(e);const t=JSON.stringify(e);localStorage.setItem("hw-counts",t)},resetCounts(){const e=this.getCounts(),t={val:0,msg_id:0===e[this.currentChatId]?.val?null:e[this.currentChatId]?.msg_id};e[this.currentChatId]=t,this.setCounts(e)},sendMessage(e){e.files?this.sendFileLocal(e):this.sendText(e)},async sendText(e){const{content:t,replyMessage:s}=e;try{const e=Wo(),o=this.messages.length,a=Zo((new Date).getTime()/1e3);let i="send";this.messages.push({_id:e,content:t,username:this.username,date:oa(),timestamp:a,saved:!1,fromMe:1,replyMessage:s,distributed:"wait"});let n={user_id:this.userId,phone:this.phone,message:t,objectId:this.objectId};s&&(n={...n,quotedMsgId:s._id,quotedMsgBody:s.content},i="reply"),this.messageInTransit=!0;const{data:r}=await it.post(`api/${i}`,n),l=r.savedMessage;r.ok?(this.messageInTransit=!1,this.$set(this.messages,o,{...this.messages[o],content:r.sentMessage,username:l.name||l.senderName,date:oa(1e3*l.time),timestamp:Zo(l.time),saved:!0,fromMe:Number(l.fromMe),replyMessage:s,distributed:!1,tmp_id:l.id})):(this.showErrorModal=!0,this.errorMessage=r.message||"Unable to send message",this.messageInTransit=!1)}catch(o){this.errorMessage=o.response?.data?.message||"Unable to send message",this.messageInTransit=!1,this.showErrorModal=!0,console.log(o)}},sendFileLocal(e){const t=Zo((new Date).getTime()/1e3);e.files.forEach(((s,o)=>{const a=0===o?e.content:"",i={_id:Wo(),content:a,username:this.username,date:oa(),timestamp:t,saved:!0,fromMe:1,files:[{...s,timestamp:t,saved:!0}]};this.messages=[...this.messages,i],this.sendFileApi(a,e.roomId,i)}))},async sendFileApi(e,t,s){let o=new FormData;o.append("file",s.files[0].rawData),o.append("message",e),o.append("phone",this.phone),o.append("user_id",this.userId),o.append("objectId",this.objectId);try{const{data:e}=await it.post("api/upload",o);if(!e.ok)throw new Error;this.updateFileState(!1,s._id,e)}catch(a){this.showErrorModal=!0,this.errorMessage="Unable to send file",this.updateFileState(!0,s._id),this.messages.pop(),console.log(a)}},updateFileState(e,t,s){const o=this.messages.find((e=>e._id===t));o&&(e?(o.files[0].error=!0,o.files[0].loading=!1):(o.tmp_id=s.messageId,o.files[0].loading=!1,o.files[0].url=s.url?s.url:o.files[0].url,o._id=s.messageId))},openFile({file:e}){window.open(e.file.url,"_blank")},toggleErrorModal(){this.showErrorModal=!this.showErrorModal,this.errorMessage=""},toggleSuccessModal(){this.showSuccessModal=!this.showSuccessModal,this.successMessage={}},closeSideBar(){this.toggleMenuBar()},redirectToHubspot(e){const t=`${this.origin}/contacts/${this.portal_id}/contact/${e.object_id}/`;window.open(t)}}},ia=aa,na=(0,u.A)(ia,I,x,!1,null,null,null),ra=na.exports;o.Ay.use(M.Ay);const la=[{path:"/",alias:"/activities",name:"Chats",component:ra},{path:"/:catchAll(.*)*",redirect:{name:"Chats"}}],ca=new M.Ay({base:"/send/",routes:la,linkExactActiveClass:"active-link"});var da=ca;o.Ay.use(n.Ay);var ua=new n.Ay.Store({state:{userData:null,menuBarShow:!1,loggedOut:!1,errorApp:!1,errorMsgApp:"",showBanner:!1,showConflict:!1,bannerContent:null,unreadCounts:{}},mutations:{setUserData:(e,t)=>{e.userData=t},toggleMenuBar:e=>{e.menuBarShow=!e.menuBarShow},logoutUser:e=>{e.loggedOut=!0},setErrorApp:(e,t)=>{e.errorApp=t},setErrorMsgApp:(e,t)=>{e.errorMsgApp=t},setBanner:(e,t)=>{e.showBanner=t},setConflict:(e,t)=>{e.showConflict=t},setBannerContent:(e,t)=>{e.bannerContent=t},setUnreadCounts:(e,t)=>{e.unreadCounts=t}},actions:{async logout(e,t){console.log(t),e.commit("logoutUser")}}}),ma=s(6989);o.Ay.config.productionTip=!1,o.Ay.use(ma.Ay);new o.Ay({router:da,store:ua,render:e=>e(k)}).$mount("#app")},137:function(e,t,s){s.d(t,{Eo:function(){return a},eG:function(){return o},ru:function(){return i}});const o=["png","jpg","jpeg","webp","svg","gif"],a=["mp4","video/ogg","webm","quicktime"],i=["mp3","audio/ogg","wav","mpeg","mpga"]},8900:function(e,t,s){function o(e){const t=[{key:"roomId",type:["string","number"]},{key:"roomName",type:["string"]},{key:"users",type:["array"]}],s=(e,t)=>t.every((t=>{let s=!1;return("array"===t.type[0]&&Array.isArray(e[t.key])||t.type.find((s=>s===typeof e[t.key])))&&(s=!0),s&&n(e,t.key)}));if(!s(e,t))throw new Error("Rooms object is not valid! Must contain roomId[String, Number], roomName[String] and users[Array]")}function a(e){const t=[{key:"_id",type:["string","number"]},{key:"username",type:["string"]}],s=(e,t)=>t.every((t=>{const s=t.type.find((s=>s===typeof e[t.key]));return s&&n(e,t.key)}));if(!s(e,t))throw new Error("Participants object is not valid! Must contain _id[String, Number] and username[String]")}function i(e){const t=[{key:"_id",type:["string","number"]},{key:"content",type:["string","number"],required:!1},{key:"username",type:["string","number"]}],s=(e,t)=>t.every((t=>{if(!t?.required)return!0;const s=t.type.find((s=>s===typeof e[t.key]));return s&&n(e,t.key)}));if(!s(e,t))throw new Error("Messages object is not valid! Must contain _id[String, Number], content[String, Number] and username[String, Number]")}function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&null!==e[t]&&void 0!==e[t]}s.r(t),s.d(t,{messagesValidation:function(){return i},partcipantsValidation:function(){return a},roomsValidation:function(){return o}})},6951:function(e,t,s){s.r(t),s.d(t,{isAudioFile:function(){return l},isImageFile:function(){return i},isImageVideoFile:function(){return r},isPdfFile:function(){return c},isVideoFile:function(){return n}});var o=s(137);function a(e,t){if(t&&t.extension)return e.some((e=>t.extension.toLowerCase().includes(e)))}function i(e){return a(o.eG,e)}function n(e){return a(o.Eo,e)}function r(e){return a(o.eG,e)||a(o.Eo,e)}function l(e){return a(o.ru,e)}function c(e){return a(["pdf"],e)}},902:function(e,t,s){function o(){const e=a(),t=e.substr(0,4);return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw(n|u)|c55\/|capi|ccwa|cdm|cell|chtm|cldc|cmd|co(mp|nd)|craw|da(it|ll|ng)|dbte|dcs|devi|dica|dmob|do(c|p)o|ds(12|d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(|_)|g1 u|g560|gene|gf5|gmo|go(\.w|od)|gr(ad|un)|haie|hcit|hd(m|p|t)|hei|hi(pt|ta)|hp( i|ip)|hsc|ht(c(| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i(20|go|ma)|i230|iac( ||\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|[a-w])|libw|lynx|m1w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|mcr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|([1-8]|c))|phil|pire|pl(ay|uc)|pn2|po(ck|rt|se)|prox|psio|ptg|qaa|qc(07|12|21|32|60|[2-7]|i)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h|oo|p)|sdk\/|se(c(|0|1)|47|mc|nd|ri)|sgh|shar|sie(|m)|sk0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h|v|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl|tdg|tel(i|m)|tim|tmo|to(pl|sh)|ts(70|m|m3|m5)|tx9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas|your|zeto|zte/i.test(t)}function a(){const e=navigator.userAgent||navigator.vendor||window.opera||null;if(!e)throw new Error("Failed to look for user agent information.");return e}function i(){return["iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}s.r(t),s.d(t,{detectMobile:function(){return o},iOSDevice:function(){return i}})}},t={};function s(o){var a=t[o];if(void 0!==a)return a.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,s),i.exports}s.m=e,function(){var e=[];s.O=function(t,o,a,i){if(!o){var n=1/0;for(d=0;d<e.length;d++){o=e[d][0],a=e[d][1],i=e[d][2];for(var r=!0,l=0;l<o.length;l++)(!1&i||n>=i)&&Object.keys(s.O).every((function(e){return s.O[e](o[l])}))?o.splice(l--,1):(r=!1,i<n&&(n=i));if(r){e.splice(d--,1);var c=a();void 0!==c&&(t=c)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[o,a,i]}}(),function(){s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,{a:t}),t}}(),function(){s.d=function(e,t){for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){s.p="/send/"}(),function(){var e={524:0};s.O.j=function(t){return 0===e[t]};var t=function(t,o){var a,i,n=o[0],r=o[1],l=o[2],c=0;if(n.some((function(t){return 0!==e[t]}))){for(a in r)s.o(r,a)&&(s.m[a]=r[a]);if(l)var d=l(s)}for(t&&t(o);c<n.length;c++)i=n[c],s.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return s.O(d)},o=self["webpackChunkchat_app"]=self["webpackChunkchat_app"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}();var o=s.O(void 0,[504],(function(){return s(1076)}));o=s.O(o)})();
//# sourceMappingURL=app.e9ffd33c.js.map