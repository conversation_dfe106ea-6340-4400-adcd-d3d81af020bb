<?php

namespace App\Jobs;

use App\Models\Campaign;
use Log;
use Exception;
use Throwable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CampaignBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;

    public function __construct($campaignId)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-campaign-batch-'.$campaignId;
    }

    public function handle(): void
    {
        $startTime = microtime(true);
        Log::info("[CampaignBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");

        $cacheKey = "campaign_updates:{$this->campaignId}";
        $batchSize = 100;
        $batch = [];

        try {
            // Process up to 100 items from Redis
            for ($i = 0; $i < $batchSize; $i++) {
                $item = Redis::lpop($cacheKey);
                if (!$item) {
                    break;
                }
                $batch[] = json_decode($item, true);
            }

            Log::info("[CampaignBatchJob:handle] {$this->requestId}, Retrieved {" . count($batch) . "} items from Redis key: {$cacheKey}");

            if (!empty($batch)) {
                try {
                    // Insert campaign data into database
                    // DB::table('campaigns')->insert($batch);
                    Campaign::insert($batch);
                    Log::info("[CampaignBatchJob:handle] {$this->requestId}, Successfully processed batch of " . count($batch) . " campaign records");
                } catch (Exception $e) {
                    // If batch insert fails, push items back to Redis
                    $dlqKey = "campaign_updates_failed:{$this->campaignId}";
                    Redis::rpush($dlqKey, json_encode([
                        'item' => $item,
                        'error' => $e->getMessage(),
                        'time' => now()->toDateTimeString(),
                    ]));
                    Redis::expire($dlqKey, 86400);
                    Log::error("[CampaignBatchJob:handle] {$this->requestId}, Batch insert failed: " . $e->getMessage());
                    
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[CampaignBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("[CampaignBatchJob:handle] {$this->requestId}, Completed in {$duration} ms");
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[CampaignBatchJob:failed] {$this->requestId}, Exception: " . $e->getMessage());
    }
}
