<?php

namespace App\Jobs;

use Log;
use Exception;
use Throwable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Cache;


class MessageBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;
    protected $keyPrefix;
    protected $lockKey;

    public function __construct($campaignId,$keyPrefix, $lockKey)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-message-batch-'.$campaignId;
        $this->keyPrefix = $keyPrefix;
        $this->lockKey = $lockKey;
    }

    public function handle(): void
    {
        $startTime = microtime(true);
        Log::info("[MessageBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");

        // $cacheKey = "message_updates:{$this->campaignId}";
        // $batchSize = 100;
        // $batch = [];

        $redisKey = "{$this->keyPrefix}:{$this->campaignId}";
        $items = Redis::lrange($redisKey, 0, -1);

        Redis::del($redisKey); // clear early to avoid double processing on retry


        try {

            foreach($items as $item){
                $batch[] = json_decode($item, true);
            }
            // Process up to 100 items from Redis
            // for ($i = 0; $i < $batchSize; $i++) {
            //     $item = Redis::lpop($cacheKey);
            //     if (!$item) {
            //         break;
            //     }
            //     $batch[] = json_decode($item, true);
            // }

            // Log::info("[MessageBatchJob:handle] {$this->requestId}, Retrieved {" . count($batch) . "} items from Redis key: {$cacheKey}");

            if (!empty($batch)) {
                try {
                    $encryptedMessages = array_map(function ($item) {
                        if (!empty($item['body'])) {
                            $item['body'] = Crypt::encryptString($item['body']);
                        }
                        return $item;
                    }, $batch);

                    DB::table('messages')->insert($encryptedMessages);
                    Cache::forget($this->lockKey);
                    Log::info("[MessageBatchJob:handle] {$this->requestId}, Successfully processed batch of " . count($batch) . " message records");
                } catch (Exception $e) {
                    $dlqKey = "message_updates_failed:{$this->campaignId}";
                    Redis::rpush($dlqKey, json_encode([
                        'item' => $item,
                        'error' => $e->getMessage(),
                        'time' => now()->toDateTimeString(),
                    ]));
                    Redis::expire($dlqKey, 86400);
                    Log::error("[MessageBatchJob:handle] {$this->requestId}, Batch insert failed: " . $e->getMessage());
                   
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[MessageBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("[MessageBatchJob:handle] {$this->requestId}, Completed in {$duration} ms");
        }
    }

    public function failed(Throwable $e)
    {
        Log::error("[MessageBatchJob:failed] {$this->requestId}, Exception: " . $e->getMessage());
    }
}
