<?php

namespace App\Services\Whatsapp;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    private const CACHE_PREFIX = 'waba:';

    private const DEFAULT_TTL = 3600; // 1 hour cache TTL

    // Cache types
    public const TEMPLATES = 'templates';

    public const MESSAGES = 'messages';

    public const ANALYTICS = 'analytics';

    public const SETTINGS = 'settings';

    // TTL configurations for different cache types
    private const TTL_CONFIG = [
        self::TEMPLATES => 3600,    // 1 hour
        self::MESSAGES => 300,      // 5 minutes
        self::ANALYTICS => 86400,   // 24 hours
        self::SETTINGS => 7200,     // 2 hours
    ];

    public function __construct(private string $requestId = '') {}

    /**
     * Get data from cache
     */
    public function get(string $wabaId, string $type, ?string $subKey = null): mixed
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
        $compressed = Cache::get($cacheKey);

        if (! $compressed) {
            return null;
        }

        $data = $this->decompress($compressed);

        Log::info("[CacheService:get] {$this->requestId}, Retrieved {$type} from cache for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));

        return $data;
    }

    /**
     * Store data in cache
     */
    public function set(string $wabaId, string $type, mixed $data, ?string $subKey = null, ?int $ttl = null): void
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
        $compressed = $this->compress($data);

        $ttl = $ttl ?? self::TTL_CONFIG[$type] ?? self::DEFAULT_TTL;
        Cache::put($cacheKey, $compressed, $ttl);

        Log::info("[CacheService:set] {$this->requestId}, Cached {$type} for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));
    }

    /**
     * Update existing cache with new data
     */
    public function update(string $wabaId, string $type, mixed $newData, ?string $subKey = null): void
    {
        $existingData = $this->get($wabaId, $type, $subKey);

        if (! $existingData) {
            $this->set($wabaId, $type, $newData, $subKey);

            return;
        }

        // Use type-specific merge logic
        $updatedData = $this->mergeData($existingData, $newData, $type);

        $this->set($wabaId, $type, $updatedData, $subKey);

        Log::info("[CacheService:update] {$this->requestId}, Updated {$type} cache for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));
    }

    /**
     * Clear specific cache
     */
    public function clear(string $wabaId, string $type, ?string $subKey = null): void
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);
        Cache::forget($cacheKey);

        Log::info("[CacheService:clear] {$this->requestId}, Cleared {$type} cache for WABA ID: {$wabaId}".($subKey ? ", subKey: {$subKey}" : ''));
    }

    /**
     * Check if cache exists
     */
    public function has(string $wabaId, string $type, ?string $subKey = null): bool
    {
        $cacheKey = $this->getCacheKey($wabaId, $type, $subKey);

        return Cache::has($cacheKey);
    }

    /**
     * Get cache key
     */
    private function getCacheKey(string $wabaId, string $type, ?string $subKey = null): string
    {
        $key = self::CACHE_PREFIX.$wabaId.':'.$type;
        if ($subKey) {
            $key .= ':'.$subKey;
        }

        return $key;
    }

    /**
     * Compress data to reduce memory usage
     */
    private function compress(mixed $data): string
    {
        $jsonString = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        return gzencode($jsonString, 9);
    }

    /**
     * Decompress data from cache
     */
    private function decompress(string $compressedData): mixed
    {
        $jsonString = gzdecode($compressedData);

        return json_decode($jsonString);
    }

    /**
     * Merge data based on type
     */
    private function mergeData(mixed $existingData, mixed $newData, string $type): mixed
    {
        return match ($type) {
            self::TEMPLATES => $this->mergeTemplates($existingData, $newData),
            default => $newData // Default: replace with new data
        };
    }

    /**
     * Merge templates by ID
     */
    private function mergeTemplates(array $existingTemplates, array $newTemplates): array
    {
        $existingMap = [];
        foreach ($existingTemplates as $template) {
            $existingMap[$template->id] = $template;
        }

        foreach ($newTemplates as $template) {
            $existingMap[$template->id] = $template;
        }

        return array_values($existingMap);
    }
}
