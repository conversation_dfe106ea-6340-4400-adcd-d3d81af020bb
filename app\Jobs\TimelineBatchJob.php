<?php

namespace App\Jobs;

use Log;
use Exception;
use App\Hubspot\Hubspot;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TimelineBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;

    public function __construct($campaignId)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-timeline-batch-'.$campaignId;
    }

    public function handle(): void
    {
        $startTime = microtime(true); // Start time
        Log::info("[TimelineBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");

        $cacheKey = "timeline_updates:{$this->campaignId}";
        $batchSize = 100;
        $batch = [];

        try {
            // Get campaign details to get portal_id
            $campaign = \App\Models\HubList::find($this->campaignId);
            if (!$campaign) {
                throw new Exception("Campaign not found: {$this->campaignId}");
            }

            $hsApp = new Hubspot($campaign->portal_id, $this->requestId);

            // Process up to 100 items from Redis
            for ($i = 0; $i < $batchSize; $i++) {
                $item = Redis::lpop($cacheKey);
                if (!$item) {
                    break;
                }
                $batch[] = json_decode($item, true);
            }

            Log::info("[TimelineBatchJob:handle] {$this->requestId}, Retrieved {" . count($batch) . "} items from Redis key: {$cacheKey}");

            if (!empty($batch)) {
                try {
                    // Call HubSpot batch update API
                    $hsApp->timeline()->updateBatch($batch, true);
                    Log::info("[TimelineBatchJob:handle] {$this->requestId}, Successfully processed batch of " . count($batch) . " items");
                } catch (Exception $e) {
                    $dlqKey = "timeline_updates_failed:{$this->campaignId}";
                    Redis::rpush($dlqKey, json_encode([
                        'item' => $item,
                        'error' => $e->getMessage(),
                        'time' => now()->toDateTimeString(),
                    ]));
                    Redis::expire($dlqKey, 86400);
                    // If batch update fails, push items back to Redis
                    Log::error("[TimelineBatchJob:handle] {$this->requestId}, Batch update failed: " . $e->getMessage());
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[TimelineBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            $duration = round((microtime(true) - $startTime) * 1000, 2); // ms
            Log::info("[TimelineBatchJob:handle] {$this->requestId}, Completed in {$duration} ms");
        }
    }

}