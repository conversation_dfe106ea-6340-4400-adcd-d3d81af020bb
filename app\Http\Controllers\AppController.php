<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Carbon\Carbon;
use App\Helpers\Func;
use App\Helpers\Waba;
use App\Models\Label;
use App\Models\Dialog;
use App\Models\Account;
use App\Models\HubList;
use App\Models\Message;
use App\Hubspot\Hubspot;
use App\Models\Campaign;
use App\Models\Workflow;
use App\Models\Blacklist;
use App\Models\ActiveUser;
use Illuminate\Http\Request;
use App\Jobs\InboxPublishJob;
use App\Models\WorkflowReport;
use App\Jobs\ProcessCampaignJob;
use App\Models\TemplateAnalytics;
use App\Models\TemplateFileLinks;
use App\Services\DashboardAccess;
use App\Services\ExportDataService;
use App\Services\Whatsapp\SendService;
use App\Services\Whatsapp\TemplateService;
use Illuminate\Support\Facades\Http;

class AppController extends Controller
{
    public function __construct()
    {
        parent::__construct('waba');
    }

    public function index(Request $request)
    {
        $scope = $request->input('scope');
        $authUrl = $this->hubspotApp->getAuthUrl($scope);
        $inboxRedirectUrl = $request->input('inboxRedirectUrl');

        return view('home', ['authUrl' => $authUrl, 'inboxRedirectUrl' => $inboxRedirectUrl]);
    }

    public function account(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
        ]);

        $portalId = readUserId($input['user_id'], 'portalId');
        $accounts = Account::where(['portal_id' => $portalId])->where('paid', '!=', 0)->get();

        return $this->jsonOk(['data' => [
            'portal_id' => $portalId,
            'licenses' => $accounts->count(),
        ]]);
    }

    public function lastStep(Request $request)
    {
        $input = $request->input();

        return view('laststep', [
            'title' => 'Successfully Connected',
            'message' => 'You can close this window and Open Hubspot and start using the integration',
            'portalId' => $input['portalId'] ?? null,
            'wabaPhoneId' => $input['waba_phone_id'] ?? null,
        ]);
    }

    public function success(Request $request)
    {
        $input = $request->input();

        return view('success', [
            'title' => 'Successfully Connected',
            'message' => 'You can close this window and Open Hubspot and start using the integration',
            'portalId' => $input['portalId'] ?? null,
            'wabaPhoneId' => $input['waba_phone_id'] ?? null,
        ]);
    }

    public function workflowEnrollmentStatus($requestId)
    {
        $workflow = Workflow::where('requestId', $requestId)->first();
        if (! $workflow) {
            return view('error', ['message' => 'No data found']);
        }

        // Set defaults
        $status = $workflow->status;
        $statusReason = $workflow->status_reason ?? null;

        // Map status to class
        $statusClasses = [
            'sent' => 'secondary',
            'delivered' => 'success',
            'read' => 'primary',
            'failed' => 'danger',
        ];
        $class = $statusClasses[$status] ?? 'secondary';

        // Define the message shown to user
        $message = match ($status) {
            'failed' => $statusReason,
            'sent' => 'Message has been sent but not yet delivered.',
            'delivered' => 'Message was successfully delivered.',
            'read' => 'Message has been read by the recipient.',
            default => 'Message is being processed.',
        };

        $duration = calculateProcessingTime($workflow->created_at, $workflow->updated_at);

        $createdAt = $workflow->created_at;
        $updatedAt = $workflow->updated_at;

        // Ensure we're passing the raw created_at and updated_at
        // without any transformations that might affect the time calculation
        return view('enrollment', [
            'requestId' => $requestId,
            'status' => $status,
            'class' => $class,
            'message' => $message,
            'duration' => $duration,
            'phone' => $workflow->phone,
            'showReason' => $status === 'failed',
            'createdAt' => $createdAt->format('M d, Y H:i'),
            'createdTime' => $createdAt->format('H:i'),
            'updatedAt' => $updatedAt->format('M d, Y H:i'),
            'updatedTime' => $updatedAt->format('H:i'),
        ]);
    }

    public function pusherAuth(Request $request)
    {
        $input = $this->validate($request, [
            'user_id' => 'required',
            'socket_id' => 'required',
            'channel_name' => 'required',
        ]);

        $accountId = readUserId($input['user_id']);

        // check if user is valid and logged in
        $user = Account::where('id', $accountId)->where('paid', 1)->first();
        if (! $user) {
            return Response('Forbidden', 403);
        }

        // check if user is trying to subscribe to intended channel
        $channelName = 'private-'.$user->portal_id.'.'.$user->waba_phone;
        if ($channelName != $input['channel_name']) {
            return Response('Forbidden', 403);
        }

        $pusher = new \Pusher\Pusher(
            env('PUSHER_APP_KEY'),
            env('PUSHER_APP_SECRET'),
            env('PUSHER_APP_ID'),
            ['cluster' => 'eu']
        );

        $auth = $pusher->socketAuth($input['channel_name'], $input['socket_id']);

        return Response($auth, 200);
    }

    public function send(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'phone' => 'required',
            'message' => 'required',
            'objectId' => 'nullable',
            'sender' => 'nullable',
        ], [
            'phone.required' => 'Kindly update your phone number in the contact details section and '.
                    'refresh the page to proceed',
        ]);

        $objectId = $input['objectId'] ?? null;
        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');
        $options = [
            'message' => $input['message'],
            'phone' => ltrim($input['phone'], '+'),
            'sender' => $input['sender'] ?? null,
        ];

        $waba = new Waba($accountId, $this->requestId);
        $portal = $waba->account;

        if (config('services.whatsapp.use_mock_api')) {
            $sendText = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-text');
            $res = json_decode($sendText->body());
            $res->customData = $waba->makeCustomData($res);
        } else {
            $res = $waba->sendText($options);
        }
       

        if ($res === false) {
            $defaultError = $this->messageSendError;
            $error = json_decode($waba->response);
            if ($error && isset($error->error->error_data->details)) {
                $errorMessage = $error->error->error_data->details;
                $errorMessage && ($defaultError = $errorMessage);
            }

            return $this->jsonError(['message' => $defaultError]);
        }

        $options['portal_id'] = $portalId;
        $message = $this->buildSentMessage($accountId, array_merge($options, $res->customData));
        $savedMessage = $this->saveMessage($message);

        $this->updateDialog([
            'account_id' => $accountId,
            'chatId' => Func::makeChatId($message['chatId']),
        ], ['time' => $message['time']]);

        if ($portal->hs_channel_id) {
            dispatch(new InboxPublishJob(
                (object) $portal,
                (object) $savedMessage->toArray(),
                $this->requestId
            ))->onQueue('wabad');
        }

        // update hubspot timeline
        $hsApp = new Hubspot($portalId, $this->requestId);
        $objectId && $hsApp->timeline()->update([
            'id' => $message['id'],
            'objectId' => $objectId,
            'data' => [
                'status' => 'sent',
                'phone' => $options['phone'],
                'message' => Func::messageToTimeline($savedMessage),
            ],
        ], true);

        return $this->jsonOk([
            'messageId' => $message['id'],
            'message' => $this->messageSendSuccess,
            'sentMessage' => Func::messageToHtml($savedMessage),
            'savedMessage' => $savedMessage,
        ]);
    }

    public function reply(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'phone' => 'required',
            'message' => 'required',
            'objectId' => 'nullable',
            'sender' => 'nullable',
            'quotedMsgId' => 'nullable',
            'quotedMsgBody' => 'nullable',
        ]);

        $objectId = $input['objectId'] ?? null;
        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');

        $quotedMsgId = $input['quotedMsgId'] ?? '';
        $quotedMsgBody = $input['quotedMsgBody'] ?? '';
        $options = [
            'message' => $input['message'],
            'phone' => ltrim($input['phone'], '+'),
            'sender' => $input['sender'] ?? null,
            'quotedMsgId' => $quotedMsgId,
            'quotedMsgBody' => $quotedMsgBody,
        ];
        
        $waba = new Waba($accountId, $this->requestId);
        $portal = $waba->account;

        if (config('services.whatsapp.use_mock_api')) {
            $sendReplay = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/replay');
            $res = json_decode($sendReplay->body());
            $res->customData = $waba->makeCustomData($res);
        } else {
            $res = $waba->sendText($options);
        }
        
        if ($res === false) {
            $this->jsonError(['message' => $this->messageSendError]);
        }

        $options['portal_id'] = $portalId;
        $message = $this->buildSentMessage($accountId, array_merge($options, $res->customData));
        $savedMessage = $this->saveMessage($message);

        $this->updateDialog([
            'account_id' => $accountId,
            'chatId' => Func::makeChatId($message['chatId']),
        ], ['time' => $message['time']]);

        if ($portal->hs_channel_id) {
            dispatch(new InboxPublishJob(
                (object) $portal,
                (object) $savedMessage->toArray(),
                $this->requestId
            ))->onQueue('wabad');
        }

        $hsApp = new Hubspot($portalId, $this->requestId);
        $hsApp->timeline()->update([
            'id' => $message['id'],
            'objectId' => $objectId,
            'data' => [
                'status' => 'sent',
                'phone' => $options['phone'],
                'message' => Func::messageToTimeline($savedMessage),
            ],
        ], true);

        return $this->jsonOk([
            'messageId' => $message['id'],
            'message' => $this->messageSendSuccess,
            'sentMessage' => Func::messageToHtml($savedMessage),
            'savedMessage' => $savedMessage,
        ]);
    }

    public function upload(Request $request)
    {
        $input = $this->validate($request, [
            'user_id' => 'required',
            'phone' => 'required',
            'file' => 'required',
            'message' => 'nullable',
            'objectId' => 'nullable',
        ]);

        try {
            $accountId = readUserId($input['user_id']);
            $portalId = readUserId($input['user_id'], 'portalId');

            $objectId = $input['objectId'] ?? null;
            $file = $this->hubspotApp->forPortal($portalId)->uploadFile($input['file']);
            $options = [
                'phone' => ltrim($input['phone'], '+'),
                'file_id' => $file->id,
                'file_type' => $file->type,
                'file_url' => $file->url,
                'filename' => $file->filename,
            ];
            isset($input['message']) && $options['message'] = $input['message'];

            $waba = new Waba($accountId, $this->requestId);
            $portal = $waba->account;
            
            if (config('services.whatsapp.use_mock_api')) {
                $sendAttechMent = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-attachment');
                $res = json_decode($sendAttechMent->body());
                $res->customData = $waba->makeCustomData($res);
            } else {
                $res = $waba->sendMedia($options);
            }
            
            if ($res === false) {
                return $this->jsonError(['message' => $this->messageSendError]);
            }

            $options['portal_id'] = $portalId;
            $message = $this->buildSentMessage($accountId, array_merge($options, $res->customData));
            $savedMessage = $this->saveMessage($message);

            $this->updateDialog([
                'account_id' => $accountId,
                'chatId' => Func::makeChatId($message['chatId']),
            ], ['time' => $message['time']]);

            if ($portal->hs_channel_id) {
                dispatch(new InboxPublishJob(
                    (object) $portal,
                    (object) $savedMessage->toArray(),
                    $this->requestId
                ))->onQueue('wabad');
            }

            $hsApp = new Hubspot($portalId, $this->requestId);
            $objectId && $hsApp->timeline()->update([
                'id' => $message['id'],
                'objectId' => $objectId,
                'data' => [
                    'status' => 'sent',
                    'phone' => $options['phone'],
                    'message' => Func::messageToTimeline($savedMessage),
                ],
            ], true);

            return $this->jsonOk([
                'messageId' => $message['id'],
                'message' => $this->messageSendSuccess,
                'sentMessage' => Func::messageToHtml($savedMessage),
                'savedMessage' => $savedMessage,
            ]);
        } catch (Exception $e) {
            Log::error("[AppController:upload] $this->requestId, for ".$e->getMessage());

            return $this->jsonError(['message' => 'Unexpected Error']);
        }
    }

    public function initiate(Request $request)
    {
        $input = $request->input();
        $fetchDestHeader = $request->header('sec-fetch-dest');
        if ($fetchDestHeader != 'iframe' && env('APP_ENV') == 'production') {
            return view('error', ['message' => 'Please open this from your HubSpot CRM Card']);
        }

        $html = file_get_contents(public_path().'/send/index.html');

        return response($html, 200)->header('Content-Type', 'text/html');
    }

    public function helpdesk(Request $request)
    {
        $input = $request->input();
        Log::info("[AppController:helpdesk] $this->requestId, payload: ".json_encode($input));

        $fetchDestHeader = $request->header('sec-fetch-dest');
        if ($fetchDestHeader != 'iframe' && env('APP_ENV') == 'production') {
            return view('error', ['message' => 'Please open this from your HubSpot CRM Card']);
        }

        try {

            if ($input['objectTypeId'] != '0-5') {
                throw new Exception('Invalid Object type', 1);
            }

            $hsApp = new Hubspot($input['portalId'], $this->requestId);
            $associations = $hsApp->associations()->get('tickets', $input['objectId'], 'contacts');
            if (! $associations || (isset($associations->results) && ! $associations->results)) {
                throw new Exception('No associated contact found for this ticket', 1);
            }
            // echo
            $contact = $hsApp->contacts()->getById($associations->results[0]->toObjectId, ['phone', 'mobilephone']);
            if (! $contact) {
                throw new Exception('Contact not found', 1);
            }

            $phone = $contact->properties->phone ?? null;
            ! $phone && $phone = $contact->properties->mobilephone ?? null;
            if (! $phone) {
                throw new Exception('Contact does not have phone number', 1);
            }

            $licenses = [];
            $delimiter = config('app.custom.delimiter');
            $accounts = Account::where('portal_id', $input['portalId'])->where('paid', '!=', 0)->get();
            foreach ($accounts as $account) {
                $query = [
                    'user_id' => simple_crypt('e', $account->id.$delimiter.$account->portal_id.$delimiter.$account->waba_phone),
                    'phone' => $phone,
                    'accountUser' => $input['email'],
                    'firstName' => $input['firstName'],
                    'lastName' => $input['lastName'],
                    'objectId' => $contact->id,
                    'portal_id' => $input['portalId'],
                    'accountPhone' => $account->waba_phone,
                ];
                $licenses[] = [
                    'name' => 'Send From: '.$account->waba_phone,
                    'link' => env('APP_URL').'/initiate?'.http_build_query($query),
                ];
            }

            return view('helpdesk', ['licenses' => $licenses]);
        } catch (Exception $e) {
            return view('error', ['message' => $e->getMessage()]);
        }
    }

    // this opens vira.niswey.net/options which renders dashboard.
    public function options(Request $request)
    {
        $input = $request->input();
        if ($input['portal_id'] != 7222284) {
            return view('error', ['message' => 'Invalid URL']);
        }

        $html = file_get_contents(public_path().'/options/index.html');

        return response($html, 200)->header('Content-Type', 'text/html');
    }

    public function dashboard(Request $request, DashboardAccess $accessManager)
    {
        // In production, verify access
        if (env('APP_ENV') == 'production' && ! $accessManager->hasAccess($request)) {
            return view('error', ['message' => 'Please open this from your HubSpot CRM Card']);
        }

        $html = file_get_contents(public_path().'/options/index.html');

        return response($html, 200)->header('Content-Type', 'text/html');
    }

    public function dialogs(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'time' => 'nullable',
            'all' => 'nullable',
            'ownerId' => 'nullable',
            'search' => 'nullable',
        ]);
        $accountId = readUserId($input['user_id']);
        $labels = $dialogs = [];
        $time = $input['time'] ?? '';
        $ownerId = $input['ownerId'] ?? '';
        $search = $input['search'] ?? '';

        $where = [['account_id', '=', $accountId]];
        $ownerId && $where[] = ['owner_id', '=', $ownerId];

        $blacklist = Blacklist::select('phone')->where('account_id', $accountId)->get();
        $blacklist && $blacklist = $blacklist->pluck('phone');
        $blacklist = $blacklist ? $blacklist->toArray() : [];

        if ($time) {
            $where[] = ['time', '<', $time];
        }

        $query = Dialog::where($where)
            ->whereNotIn('chatId', $blacklist);

        if (! empty($search)) {
            $normalizedSearch = str_replace(' ', '', strtolower($search));
            $query->where(function ($q) use ($normalizedSearch) {
                $q->whereRaw("REPLACE(LOWER(name), ' ', '') LIKE ?", ["%{$normalizedSearch}%"])
                    ->orWhereRaw("REPLACE(LOWER(phone), ' ', '') LIKE ?", ["%{$normalizedSearch}%"]);
            });
        } else {
            $query->limit(300);
        }
        $query->orderByDesc('time');
        $dialogs = $query->get();

        // send labels on default request
        ! $time && ($labels = Label::where('account_id', $accountId)->get());

        return response()->json(['status' => 'ok', 'data' => $dialogs, 'labels' => $labels]);
    }

    public function chat(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'chatId' => 'required',
            'type' => 'nullable',
            'time' => 'nullable',
        ]);

        $time = $input['time'] ?? null;
        $type = $input['type'] ?? 'dashboard';
        $accountId = readUserId($input['user_id']);

        if ($type == 'crm') {
            // return all messages in case of show coversation for single contact as no pagination is there
            $messages = Message::where([
                ['chatId', '=', $input['chatId']],
                ['account_id', '=', $accountId],
            ])->orderBy('time')->limit(2000)->get();
        } else {
            $queryFields = [
                ['chatId', '=', $input['chatId']],
                ['account_id', '=', $accountId],
            ];
            $time && $queryFields[] = ['time', '<', $time];
            $messages = Message::where($queryFields)->orderByDesc('time')->limit(500)->get();
        }

        if (! $messages) {
            return res('error', 'No messages found');
        }

        $allMessages = [];
        $mediaTypes = ['image', 'video', 'document', 'application', 'sticker', 'audio'];
        foreach ($messages as $message) {
            if (in_array($message->type, $mediaTypes)) {
                $filename = $message->file_name ?? null;
                ! $filename && $filename = pathinfo($message->file_url, PATHINFO_FILENAME);
                $file = [
                    'name' => $filename,
                    'url' => $message->file_url,
                    'extension' => pathinfo($message->file_url, PATHINFO_EXTENSION),
                ];
                $message->files = [$file];
            }
            $allMessages[] = $message;
        }

        return response()->json([
            'status' => 'ok',
            'messages' => $allMessages,
        ]);
    }

    public function changeDialog(Request $request, $id)
    {
        $input = $request->validate([
            'pinned' => 'required',
        ]);

        try {
            Dialog::where('id', $id)->update($input);

            return $this->jsonOk(['message' => 'Successfully updated']);
        } catch (Exception $e) {
            Log::error("[AppController:changeDialog] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError(['message' => 'Unexpected Error']);
        }
    }

    public function whatsappTemplates(Request $request)
    {
        $input = $request->validate(['user_id' => 'required']);
        $accountId = readUserId($input['user_id']);

        $formattedTemplate = [];

        $version = $input['version'] ?? null;
        
        if (config('services.whatsapp.use_mock_api')) {
            ini_set('max_execution_time', 180);
            $response = Http::timeout(120)->get(config('services.whatsapp.mock_api_url').'whatsapp/fetch-template');
            $templates = json_decode($response->body());
            return response()->json($templates);
        }

        $waba = new Waba($accountId, $this->requestId);
        $templates = $waba->fetchTemplates($accountId);
    
        if (! $templates) {
            return $this->jsonError([
                'message' => 'unable to fetch templates, check if facebook access token is valid',
            ]);
        }
        
        foreach ($templates as $template) {
            $templateService = new TemplateService($template);
            $template = $templateService->analyzeTemplate();
            $formattedTemplate[] = $template;
        }

        return $this->jsonOk(['data' => $formattedTemplate]);
        
    }

    public function getLists(Request $request)
    {
        $input = $request->validate(['user_id' => 'required']);
        $accountId = readUserId($input['user_id']);

        $lists = HubList::where('account_id', $accountId)->limit(300)->latest()->get();

        return $this->jsonOk(['data' => $lists]);
    }

    public function storeLists(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'template.id' => 'required',
            'list.id' => 'required',
            'list.name' => 'required',
            'list.size' => 'required',
            'list.type' => 'required',
            'list.timezone' => 'nullable',
            'list.schedule_time' => 'nullable',
            'list.lastSizeChangeAt' => 'required',
        ]);
        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');
        $timezone = $request->header('TimeZoneOffset') ?: $request->input('list.timezone');
        $scheduleTime = $request->input('list.schedule_time');

        $updateData = [
            'account_id' => $accountId,
            'listId' => $request->input('list.id'),
            'portal_id' => $portalId,
            'campaign_name' => $request->input('list.campaign_name'),
            'name' => $request->input('list.name'),
            'size' => $request->input('list.size'),
            'template_id' => $request->input('template.id'),
            'data' => json_encode($request->input('template')),
            'list_type' => $request->input('list.type'),
            'timezone' => $timezone ? $timezone : null,
            'schedule_time' => $scheduleTime ? $scheduleTime : null,
            'last_size_change_at' => $request->input('list.lastSizeChangeAt'),
            'last_processed' => null,
            'processed' => 0,
            'sent' => 0,
            'delivered' => 0,
            'viewed' => 0,
            'failed' => 0,
        ];
        $request->input('list.enabled') && ($updateData['enabled'] = $request->input('list.enabled')); // set enabled based on cu
        $request->input('list.schedule_time') && ($updateData['enabled'] = 0); // disable by default if schedule date is set

        $campaign = HubList::create($updateData);
        if (! $scheduleTime) {
            dispatch(new ProcessCampaignJob($campaign->id))->onQueue('lists');
        }

        return $this->jsonOk(['data' => $campaign]);
    }

    public function updateLists(Request $request, $id)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'enabled' => 'required',
        ]);
        $accountId = readUserId($input['user_id']);

        $list = HubList::where(['account_id' => $accountId, 'id' => $id])->update(['enabled' => $input['enabled']]);

        return $this->jsonOk(['data' => $list]);
    }

    public function banner(Request $request)
    {
        $input = $request->input();
        Log::info("[AppController:banner] $this->requestId, banner called for", $input);

        $portalId = readUserId($input['user_id'] ?? '', 'portalId') ?? '';

        $endTime = **********;
        $banner = time() > $endTime ? [] : [
            'banner' => [
                'html' => '<strong>Please note:</strong> Support will be limited from <strong>March 26 to March 28, 2025</strong>,
		                   due to a company offsite. We will be back and responding to queries on <strong>March 31, 2025</strong>.
		                   If you have any support-related inquiries, please do not hesitate to submit a ticket
		                   <a href="https://share.hsforms.com/1Is0DNg6XS8yM6PgC4VVVuA1mb0n" target="_blank" rel="noopener noreferrer">here</a>.',
            ],
            'banner_time' => $endTime,
            'platforms' => ['vira', 'messagebird'],
        ];

        return $this->jsonOk($banner);
    }

    public function sendTemplate(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'phone' => 'required',
            'objectId' => 'nullable',
            'templateId' => 'required',
            'fields' => 'nullable',
        ]);

        $fields = $input['fields'] ?? [];
        $objectId = $input['objectId'] ?? null;
        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId') ?? '';

        $waba = new SendService($accountId, $this->requestId);

        if (config('services.whatsapp.use_mock_api')) {
           
            $response = Http::get(config('services.whatsapp.mock_api_url').'templates/*********');
            $template = json_decode($response->body())->template;
            
        } else {
        
            $template = $waba->fetchTemplateById($input['templateId']);
            $templateService = new TemplateService($template);
            $template = $templateService->analyzeTemplate();
        }
        
        $options = [];
        $portal = $waba->account;
        $hsApp = $this->hubspotApp;

        $options['phone'] = $phone = Func::makeChatId($input['phone']);

        $properties = null;
        $options['params'] = [];
        $options['templateData'] = [];
        $options['template'] = $template;
        
        // convert contact tokens to value
        $properties = [];
        foreach ($fields as $key => $value) {
            if (preg_match('/\[(.*?)\]/', $value, $matches)) {
                $properties[$key] = $matches[1];
            }
        }
        if ($properties && $objectId) {
            $contact = $hsApp->forPortal($portalId)->contacts()->getById($objectId, $properties);
            $contactProperties = $contact->properties ?? [];
            foreach ($properties as $key => $value) {
                $fields[$key] = $contactProperties->{$value} ?? null;
            }
        }
        
        // build params
        $namedParams = $template->named ?? [];
        $paramCount = $template->params;

        for ($i = 0; $i < $paramCount; $i++) {
            $placeholder = $fields['placeholder_'.($i + 1)] ?? '';

            $param = [
                'type' => 'text',
                'text' => $placeholder,
            ];

            if (isset($namedParams[$i])) {
                $param['parameter_name'] = $namedParams[$i];
            }

            $options['params'][] = $param;
            $options['templateData'][] = $placeholder;
        }

        $options = array_merge($options, $fields);
        
        if (config('services.whatsapp.use_mock_api')) {
            $sendTemplate = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-template');
            $res = json_decode($sendTemplate->body());
            $res->customData = $waba->makeCustomData($res);
            
        } else {
            if ($template->type && $template->type != 'text') {
                $res = $waba->sendMediaTemplate($options);
            } else {
                $res = $waba->sendTemplate($options);
            }
        }
        

        if ($res === false || ! isset($res->customData['messageId'])) {
            $errorResponse = json_decode($waba->response);
            $errorMessage = $errorResponse->error->error_data->details ?? 'Unable to send message';

            return $this->jsonError(['message' => $errorMessage]);
        }

        $messageBody = $template->body;
        for ($i = 0; $i < count($options['templateData']); $i++) {
            $key = $namedParams ? $namedParams[$i] : ($i + 1);
            $messageBody = str_replace('{{'.$key.'}}', $options['templateData'][$i], $messageBody);
        }

        Dialog::where('chatId', $options['phone'])
            ->where('account_id', $accountId)
            ->update(['time' => time()]);

        $type = $template->mediaType ?? 'text';
        if (! in_array($type, ['image', 'audio', 'video', 'document'])) {
            $type = 'text';
        }
        $message = [
            'account_id' => $accountId,
            'id' => $res->customData['messageId'],
            'chatId' => $options['phone'],
            'type' => strtolower($type),
            'body' => $messageBody,
            'from' => $res->customData['from'],
            'to' => $phone,
            'fromMe' => 1,
            'status' => 'sent',
            'time' => time(),
        ];
        $flowId = $res->customData['flowId'] ?? null;
        $flowId && $message['flow_id'] = $flowId;

        $urlKey = strtolower($type).'_url';
        $fileUrl = $fields[$urlKey] ?? null;
        $type && $message['file_type'] = strtolower($type);
        $fileUrl && $message['file_url'] = $fileUrl;
        $fileUrl && $message['file_name'] = basename($fileUrl);
        $savedMessage = Message::create($message);

        if ($portal->hs_channel_id) {
            dispatch(new InboxPublishJob(
                (object) $portal,
                (object) $savedMessage->toArray(),
                $this->requestId
            ))->onQueue('wabad');
        }

        // update hubspot timeline
        $hsApp = new Hubspot($portalId, $this->requestId);
        $objectId && $hsApp->timeline()->update([
            'id' => $res->customData['messageId'],
            'objectId' => $objectId,
            'data' => [
                'status' => 'sent',
                'phone' => $portal->waba_phone,
                'message' => Func::messageToTimeline($savedMessage),
            ],
        ], true);

        Log::info("[AppController:sendTemplate] success for $this->requestId");

        if ($fileUrl) {
            $savedMessage->files = [
                [
                    'name' => pathinfo($fileUrl, PATHINFO_FILENAME),
                    'url' => $fileUrl,
                    'extension' => pathinfo($fileUrl, PATHINFO_EXTENSION),
                ],
            ];
        }

        return $this->jsonOk(['message' => $savedMessage]);
    }

    public function campaignExport(Request $request)
    {
        $input = $request->validate([
            'id' => 'required|integer',
            'user_id' => 'required|string',
        ]);

        $campaign = Campaign::with('hub_campaign_detail')
            ->where('account_id', readUserId($input['user_id']))
            ->where('campaign_id', $input['id'])
            ->first();
        if (empty($campaign)) {
            return view('error', ['message' => 'No data found']);
        }

        return (new ExportDataService(
            // Filename for the exported CSV (dynamically generated)
            $campaign->hub_campaign_detail->name.'_Report_'.Carbon::now()->format('Ymd'),

            // Headers for the CSV file
            [
                'Campaign Name', 'Template Used Name', 'Contact Name', 'Email ID',
                'Phone Number', 'Status', 'Failed Reason', 'Reply', 'Timestamp (Sent Message)',
            ],

            // Query to fetch data in chunks for efficient export
            Campaign::leftJoin('hs_campaign_lists', 'campaigns.campaign_id', '=', 'hs_campaign_lists.id')
                ->where('campaigns.account_id', $campaign->account_id)
                ->where('campaigns.campaign_id', $campaign->campaign_id)
                ->select([
                    'hs_campaign_lists.name as campaign_name',
                    'hs_campaign_lists.data->name as template',  // JSON extraction
                    'campaigns.name',
                    'campaigns.email',
                    'campaigns.phone',
                    'campaigns.status',
                    'campaigns.status_reason',
                    'campaigns.reply_message',
                ])
                ->selectRaw("DATE_FORMAT(campaigns.created_at, '%Y-%m-%d %H:%i:%s') as formatted_created_at"),
            'campaign'))->streamCsv();
    }

    public function workflowExport(Request $request)
    {
        $input = $request->validate([
            'id' => 'required|integer',
            'user_id' => 'required|string',
        ]);

        $workflow = WorkflowReport::where('account_id', readUserId($input['user_id']))
            ->where('id', $input['id'])
            ->first();

        if (empty($workflow)) {
            return view('error', ['message' => 'No data found']);
        }

        return (new ExportDataService(
            // Filename for the exported CSV (dynamically generated)
            $workflow->name.'_Report_'.Carbon::now()->format('Ymd'),

            // Headers for the CSV file
            [
                'Workflow Name', 'Template Used Name', 'Contact Name',
                'Phone Number', 'Status', 'Failed Reason', 'Timestamp (Sent Message)',
            ],

            // Query to fetch data in chunks for efficient export
            Workflow::leftJoin('workflow_reports', 'workflows.workflowId', '=', 'workflow_reports.id')
                ->where('workflows.account_id', $workflow->account_id)
                ->where('workflows.workflowId', $workflow->id)
                ->select([
                    'workflow_reports.name as workflow_name',
                    'workflows.template_name',
                    'workflows.name',
                    'workflows.phone',
                    'workflows.status',
                    'workflows.status_reason',
                ])
                ->selectRaw("DATE_FORMAT(workflows.created_at, '%Y-%m-%d %H:%i:%s') as formatted_created_at"),
            'workflow'))->streamCsv();
    }

    public function getTemplateMediaUrl(Request $request, $id)
    {
        $input = $request->validate([
            'user_id' => 'required|string',
        ]);

        $accountId = readUserId($input['user_id']);

        try {
            $template = TemplateFileLinks::where(['template_id' => $id])->first();
            if (empty($template)) {
                throw new Exception('Data not found');
            }
            $responseData = [
                'file_url' => $template->file_url,
                'file_type' => $template->file_type,
            ];

            return $this->jsonOk($responseData);
        } catch (Exception $e) {
            Log::error("[AppController:getTemplateMediaUrl] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError();
        }
    }

    public function getTemplateAnalytics(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'from' => 'required',
            'to' => 'required',
        ]);
        $accountId = readUserId($input['user_id']);
        try {
            $template_analytics_data = TemplateAnalytics::where('start_unix_time', '>=', strtotime($input['from']))->where('end_unix_time', '<=', strtotime($input['to']))->where('account_id', $accountId)->groupBy('template_id', 'template_name', 'total_buttons')->select('template_id', 'template_name', 'total_buttons')->selectRaw('SUM(send_message_count) as total_send_message_count, SUM(delivered_message_count) as total_delivered_message_count, SUM(read_message_count) as total_read_message_count, SUM(click_button_count) as total_click_button_count')->get();
            $rates = [];
            foreach ($template_analytics_data as $point) {
                $sent = $point->total_send_message_count;
                $sent_divide = $point->total_send_message_count ? $point->total_send_message_count : 1;
                $read = $point->total_read_message_count;
                $delivered = $point->total_delivered_message_count;
                $template_id = $point->template_id;
                $clicked = $point->total_click_button_count;
                $total_buttons_divide = $point->total_buttons ? $point->total_buttons : 1;
                $template_name = $point->template_name;
                $click_rate = ($clicked / ($sent_divide * $total_buttons_divide)) * 100;
                $reply_rate = ($read / $sent_divide) * 100;
                $delivery_rate = ($delivered / $sent_divide) * 100;

                $rates[] = [
                    'template_id' => $template_id,
                    'template_name' => $template_name,
                    'click_rate' => round($click_rate),
                    'reply_rate' => round($reply_rate),
                    'delivery_rate' => round($delivery_rate),
                    'read' => $read,
                    'clicked' => $clicked,
                    'delivered' => $delivered,
                    'sent' => $sent,
                ];
            }

            return $this->jsonOk(['data' => $rates]);
        } catch (Exception $e) {
            Log::error("[AppController:getTemplateAnalytics] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError();
        }
    }

    public function storeActiveUserAnalytics(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required|string',
            'email' => 'required|string',
        ]);

        $accountId = readUserId($input['user_id']);
        $portalId = readUserId($input['user_id'], 'portalId');
        $currentTime = now();

        try {
            $activeSession = ActiveUser::where('account_id', $accountId)
                ->where('email', $input['email'])
                ->latest('last_active_at')
                ->first();

            if ($activeSession) {
                if ($activeSession->last_active_at->diffInMinutes($currentTime) <= 30) {
                    $activeSession->update(['last_active_at' => $currentTime]);
                } else {
                    $activeSession->endSession();
                    ActiveUser::startNewSession($accountId, $portalId, $input['email'], $currentTime);
                }
            } else {
                ActiveUser::startNewSession($accountId, $portalId, $input['email'], $currentTime);
            }

            return $this->jsonOk();
        } catch (Exception $e) {
            Log::error("[AppController:storeActiveUserAnalytics] $this->requestId, Exception: ".$e->getMessage());

            return $this->jsonError();
        }
    }

    public function deploy()
    {
        // Check if the environment is production
        if (app()->environment('production')) {
            return response()->json([
                'ok' => false,
                'message' => 'Deployment is only allowed on the staging environment.',
            ], 403);
        }

        // Define the path to the project directory
        $projectPath = '/var/www/viradev.niswey.net';

        // Run `git pull origin staging` inside the project directory
        $command = "cd {$projectPath} && git pull origin staging 2>&1";
        $output = shell_exec($command);

        // Check if the output contains "Already up to date" or any error
        if (strpos($output, 'Already up to date') !== false) {
            $message = 'No new changes to deploy. Already up to date.';
        } elseif (strpos($output, 'error') !== false || strpos($output, 'fatal') !== false) {
            $message = 'Error during deployment: '.$output;

            return response()->json([
                'ok' => false,
                'message' => $message,
            ], 500);
        } else {
            $message = 'Deployment successful! Output: '.$output;
        }

        return response()->json([
            'ok' => true,
            'message' => $message,
        ]);
    }

    public function checkMedia(Request $request)
    {
        $input = $request->validate([
            'user_id' => 'required',
            'type' => 'required|in:video,audio,document,image',
            'url' => 'required|url',
        ]);

        $this->logger->log('payload, '.json_encode($input));

        try {
            $accountId = readUserId($input['user_id']);
            if (! is_numeric($accountId)) {
                throw new Exception('Invalid user id', 1);
            }

            // Get file headers using a HEAD request
            $context = stream_context_create([
                'http' => ['method' => 'HEAD'],
            ]);
            $headers = get_headers($input['url'], 1, $context);

            if (! $headers) {
                throw new Exception('Could not retrieve file headers', 1);
            }

            // Extract content type and size
            $mimeType = $headers['Content-Type'] ?? '';
            if (is_array($mimeType)) {
                $mimeType = $mimeType[0];
            }

            $fileSize = $headers['Content-Length'] ?? 0;
            if (is_array($fileSize)) {
                $fileSize = (int) $fileSize[1];
            } else {
                $fileSize = (int) $fileSize;
            }

            $allowed = Func::isMediaAllowed($input['type'], $mimeType, $fileSize);

            return $this->jsonOk(['allowed' => $allowed, 'size' => round($fileSize / (1024 * 1024), 2)]);

        } catch (Exception $e) {
            $this->logger->exception($e);

            return $this->jsonError(['message' => 'Oops, something went wrong']);
        }
    }

    public function addDummySub()
    {
        return view('dummysub');
    }

    public function test(Request $request)
    {
        $fbApp = $this->waba->fbApp;

        return view('facebook', [
            'app' => $this->app,
            'mode' => 'mobile',
            'properties' => [],
            'portalId' => 7222284,
            'fbVersion' => $fbApp['version'],
            'fbAppId' => $fbApp['client_id'],
            'title' => 'Connect '.$this->app,
            'fbConfigId' => $fbApp['config_id'],
            'fbAuthUrl' => $this->waba->getAuthUrl(),
        ]);
    }
}
