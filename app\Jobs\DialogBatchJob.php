<?php

namespace App\Jobs;

use Log;
use Exception;
use App\Hubspot\Hubspot;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Dialog;

class DialogBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;
    protected $keyPrefix;
    protected $lockKey;

    public function __construct($campaignId, $keyPrefix, $lockKey)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-timeline-batch-'.$campaignId;
        $this->keyPrefix = $keyPrefix;
        $this->lockKey = $lockKey;
    }

    public function handle(): void
    {
        $startTime = microtime(true); // Start time
        Log::info("[DialogBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");

        $redisKey = "{$this->keyPrefix}:{$this->campaignId}";
        $batchSize = 100;
        $batch = [];

        try {
            // Process up to 100 items from Redis using LPOP to avoid duplicates
            for ($i = 0; $i < $batchSize; $i++) {
                $item = Redis::lpop($redisKey);
                if (!$item) {
                    break; // No more items
                }
                $batch[] = json_decode($item, true);
            }

            Log::info("[DialogBatchJob:handle] {$this->requestId}, Retrieved {" . count($batch) . "} items from Redis key: {$redisKey}");

            if (!empty($batch)) {
                try {
                    // Call HubSpot batch update API
                     Dialog::upsert($batch, ['chatId', 'account_id'], ['object_id', 'phone', 'name', 'time']);
                     Log::info("[DialogBatchJob] Inserted batch of " . count($batch) . " dialog records for campaign: {$this->campaignId}");
                } catch (Exception $e) {
                    // If batch update fails, push items back to Redis
                    $dlqKey = "dialog_updates_failed:{$this->campaignId}";
                    Redis::rpush($dlqKey, json_encode([
                        'item' => $item,
                        'error' => $e->getMessage(),
                        'time' => now()->toDateTimeString(),
                    ]));
                    Redis::expire($dlqKey, 86400);
                    Log::error("[DialogBatchJob:handle] {$this->requestId}, Batch update failed: " . $e->getMessage());
                    
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[DialogBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            // Always release the lock when job completes
            Cache::forget($this->lockKey);
            $duration = round((microtime(true) - $startTime) * 1000, 2); // ms
            Log::info("[DialogBatchJob:handle] {$this->requestId}, Completed in {$duration} ms, lock released");
        }
    }

}