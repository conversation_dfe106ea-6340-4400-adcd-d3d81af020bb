@extends('layouts.new')
@section('title')
	{{$title}}
@endsection

@section('content')
	<style type="text/css">
		.acontent p, .acontent label {
			color: #516f90;
			font-size: 18px;
		}
		.consent-check {
			font-size: 14px !important;
			line-height: 1.5em;
			margin-left: 10px;
		}
	</style>
	<header>
	    <div class="container">
	        <div class="logo_main">
	            <a href="/">
	            	<img src="/img/niswey-logo.png" width="60" alt="niswey-logo" />
	            </a>
	        </div>
	    </div>
	</header>

	<section id="content" class="container">
	    <div class="flex-container">
	        <div class="leftcontent">
	            <h1>{{env('APP_NAME')}}: Create the buzz around your business</h1>
	            <p class="paratext">
	            	Integrate your WhatsApp Business Accounts (WABA) with HubSpot. Send out WhatsApp marketing campaigns to your HubSpot contacts lists, track all WhatsApp conversations in HubSpot, and do it all on the free HubSpot plan or enterprise, or anything in between
				</p>
	        </div>
	        <div class="rightcontent">
	        	<img src="/img/authorize.png" alt="{{env('APP_NAME')}}" />
	        </div>
	    </div>
	</section>
	<section class="container margin_top pb-5">
	    <div class="custom-progress-bar">
	        <div class="custom-progress-step  custom-progress-step-active custom-progress-step-complete" data-title="AUTHORIZE"></div>
	        <div class="custom-progress-connector active"></div>
	        <div class="custom-progress-step custom-progress-step-active" data-title="WHATSAPP CREDENTIALS"></div>
	        <div class="custom-progress-connector"></div>
	        <div class="custom-progress-step" data-title="SET UP HubSpot"></div>
	    </div>
	    <div class="accwrap">
	        <button class="accordion disabled completed">Authorize your HubSpot portal</button>
	        <button class="accordion active">Connect your WhatsApp Business account</button>
	        <div class="panel active" x-data="data()">
	            <div class="panelbox">
	            	<div class="acontent">
	            		<div class="container-sm py-2">
		            		<template x-if="failed">
								<div class="text-center">
									<div class="alert alert-danger" role="alert" x-text="failed"></div>
								</div>
							</template>
							<template x-if="success">
								<div class="text-center">
									<div class="alert alert-success" role="alert" x-text="success"></div>
								</div>
							</template>
							<template x-if="loading">
								<div class="text-center">
									<div class="spinner-border text-primary" role="status">
										<span class="visually-hidden">Loading...</span>
									</div>
								</div>
							</template>
	            		</div>

	            		<div class="portal-info mb-4 mt-2">
							<div class="d-flex">
								<div class="mr-2">
									<strong>For Portal ID: </strong> {{$portalId}}
								</div>
							</div>
						</div>

						<form @submit.prevent="whatsAppSignup()">
							<div class="form-group mt-5">
	                            <div class="mb-3 row">
	                                <label for="property_consent" class="col-auto">Do you have a HubSpot property that collects consent to send WhatsApp messages?</label>
	                                <div class="col-auto">
	                                    <select x-on:change="checkConsentProperty($event)" x-model="hasConsentValue" required id="property_consent" class="form-select">
	                                        <option value="">Select</option>
	                                        <option value="yes">Yes</option>
	                                        <option value="no">No</option>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>

	                        <template x-if="hasConsentProperty">
	                            <div class="form-group property_consent_drop mb-3">
	                                <label for="consent_property" class=" mb-1"> Select your 'consent' property from the dropdown below (only Single Checkbox properties are allowed)</label>
	                                <select x-model="consentPropertyName" required  id="property_consent_container" class="form-select" name="consent_property">
	                                    <option value="">Select</option>
	                                    <template x-for="property in properties">
	                                        <option x-bind:value="property.name" x-text="property.label"></option>
	                                    </template>
	                                </select>
	                            </div>
	                        </template>

	                        <div x-show="showPropertyCreateMessage">
	                            <div class="form-group text-warning mb-3">
	                                <li class="list-group-item list-group-item-info list-flex">
	                                    <div class="px-2 py-2">
	                                        <p><strong>Please note:</strong></p>
	                                        <p>{{env('APP_NAME')}} will create a HubSpot property to collect and manage consent for receiving WhatsApp messages.</p>
	                                        <p>This new property is named "Yes, I consent to receive WhatsApp messages"</p>
	                                        <p>Find more about this property here.</p>
	                                    </div>
	                                </li>
	                            </div>
	                        </div>
	                        <div>
								<p>
									Connect your WhatsApp Business account to HubSpot (or create a new one) to start receiving WhatsApp messages in your conversations inbox. You will need a WhatsApp number to complete this setup.
								</p>
								<div class="d-flex mt-5">
									<div>
										<input type="checkbox" x-model="gaveConsent" x-on:change="checkConsentAccepted($event)" />
									</div>
									<p class="consent-check ml-2">
										Gathering and maintaining customer consent for WhatsApp communications, in the above-mentioned HubSpot property, is the sole responsibility of ‘the user’ (you)/the business entity you represent. The application provider (Niswey) is not responsible for gathering and maintaining customer consent in any form.
									</p>
								</div>
	                            <button :disabled="disableRegister" type="submit" class="btn1">Continue with WhatsApp</button>
							</div>
						</form>
	            	</div>

	            </div>
	        </div>
	        <button class="accordion disabled">Setup HubSpot</button>
	    </div>
	</section>


	<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.10/clipboard.min.js"></script>
	<script src="//unpkg.com/alpinejs" defer></script>
	<script type="text/javascript">
		window.onload = () => {
			let activeAccordion = document.querySelector('.accordion.active');
			activeAccordion && activeAccordion.scrollIntoView({ block: 'start',  behavior: 'smooth' });
		}

		window.axios.defaults.headers.common = {
			"mode": "no-cors",
			"Content-Type": "application/json",
			"X-CSRF-TOKEN" : document.querySelector('meta[name="csrf-token"]').getAttribute("content")
		};

		function data() {
			return {
				loading: false,
				failed: "",
				success: "",
				portalId: "{{$portalId}}",
				appId: "{{$fbAppId}}",
				fbAuthUrl: '<?php echo strip_tags($fbAuthUrl);?>',
				properties: JSON.parse('<?=json_encode($properties)?>'),
				wabaPhones: [],
				wabaId: null,
				wabaToken: null,
				consentProperty: null,
				wabaSelectedPhone: null,
				hasConsentValue: null,
				hasConsentProperty: false,
				consentPropertyName: null,
				wabaPhoneId: null,
				wabaPhone: null,
				showPropertyCreateMessage: false,
				disableRegister: true,
				gaveConsent: false,
				appUrl: '<?=env('APP_URL')?>',

				resetMessage() {
					var self = this;
					setTimeout(function() {
						self.failed  = "";
						self.success  = "";
					}, 2000)
				},

				saveFacebookToken(event) {
					let data = {
						id: this.wabaId,
						token: this.wabaToken,
					};

					this.loading = true;
					var self = this;
					let url = self.appUrl+'/api/waba_numbers';
					axios.post(url, data)
					.then(function (response) {
						self.loading = false;
						let res = response.data;
						if(!res.ok) {
							self.failed = res.message;
						} else {
							self.wabaPhones = res.waba_phones;
							console.log(res.waba_phones)
						}
						self.resetMessage();
				    })
				    .catch(function (error) {
				    	this.loading = false;
				        console.log(error);
				    });
				},

				register(event) {
					let data = {
						waba_id: this.wabaId,
						token: this.wabaToken,
						portal_id: this.portalId,
						waba_phone: this.wabaPhone,
						waba_phone_id: this.wabaPhoneId,
						consent_property: this.consentPropertyName
					};

					this.loading = true;
					var self = this;
					let url = self.appUrl+'/api/register';
					axios.post(url, data)
					.then(function (response) {
						self.loading = false;
						let res = response.data;
						if(!res.ok) {
							self.failed = res.message;
						} else {
							window.location.href = self.appUrl+'/success?portal_id='+self.portalId;
						}
						self.resetMessage();
				    })
				    .catch(function (error) {
				    	this.loading = false;
				        console.log(error);
				    });
				},

				checkConsentAccepted(event) {
					this.disableRegister = !event.currentTarget.checked;
				},

				checkConsentProperty(event) {
					if(this.hasConsentValue == 'yes') {
						this.showPropertyCreateMessage = false;
						this.hasConsentProperty = true;
					}

					if(this.hasConsentValue == 'no') {
						this.hasConsentProperty = false;
						this.showPropertyCreateMessage = true;
						this.consentPropertyName = null;
					}
					console.log("here");
					console.log(this.hasConsentValue)
				},

				whatsAppSignup(event) {
					let data = {
						portalId: this.portalId,
						consentProperty: this.hasConsentProperty ? this.consentPropertyName : null,
					}
					launchWhatsAppSignup(data, this.fbAuthUrl);
				}

				// functions ends
			}
		}

		function launchWhatsAppSignup(data = null, fbAuthUrl) {
			let state = '&state='+encodeURIComponent(JSON.stringify(data));
			let url = fbAuthUrl+state;

			var width = 650;
			var height = 1063;
			var left = (window.innerWidth - width) / 2;
			var top = (window.innerHeight - height) / 2;

			// Ensure the top position is at the middle
			if (top < 0) {
			    top = 0;
			}
			var features = 'width=' + width + ',height=' + height + ',top=' + top + ',left=' + left + ',resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=no,directories=no,status=no';

			var isPopup = (window.opener && !window.opener.closed);
		    if (!isPopup) {
		        // Define popup features
		        var features = 'width=600,height=600,scrollbars=yes,resizable=yes';

		        // Open the popup window
		        var popup = window.open(url, 'Facebook Authentication', features);

		        // Focus the window if it's already opened
		        if (popup && popup.focus) {
		            popup.focus();
		        }
		    } else {
		        // If already inside a popup, redirect instead
		        window.location.href = url;
		    }
		}

		function processChildMessage(json) {
			let data = JSON.parse(json);
			if(data.success) {
				window.location.href = '<?=env('APP_URL')?>'+'/last-step?portalId='+data.portalId+'&waba_phone_id='+data.wabaPhoneId
			}
		}
	</script>

@endsection('content')
